var I=Object.defineProperty,S=Object.defineProperties,R=Object.getOwnPropertyDescriptors,N=Object.getOwnPropertySymbols,q=Object.prototype.hasOwnProperty,K=Object.prototype.propertyIsEnumerable,D=(e,r,t)=>r in e?I(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,k=(e,r)=>{for(var t in r||(r={}))q.call(r,t)&&D(e,t,r[t]);if(N)for(var t of N(r))K.call(r,t)&&D(e,t,r[t]);return e},x=(e,r)=>S(e,R(r)),m=(e,r,t)=>new Promise((n,l)=>{var o=i=>{try{d(t.next(i))}catch(c){l(c)}},a=i=>{try{d(t.throw(i))}catch(c){l(c)}},d=i=>i.done?n(i.value):Promise.resolve(i.value).then(o,a);d((t=t.apply(e,r)).next())}),z=(e,r)=>{const t=`https://data.jsdelivr.com/v1/package/npm/${r}`;return g(e,t,{cache:"no-store"})},A=(e,r,t)=>{const n=`https://data.jsdelivr.com/v1/package/resolve/npm/${r}@${t}`;return g(e,n)},B=(e,r,t)=>m(void 0,null,function*(){const n=`https://data.jsdelivr.com/v1/package/npm/${r}@${t}/flat`,l=yield g(e,n);return l instanceof Error?l:x(k({},l),{moduleName:r,version:t})}),T=(e,r,t,n)=>m(void 0,null,function*(){const l=`https://cdn.jsdelivr.net/npm/${r}@${t}${n}`,a=yield(e.fetcher||fetch)(l);return a.ok?a.text():new Error("OK")});function g(e,r,t){return(e.fetcher||fetch)(r,t).then(l=>l.ok?l.json().then(o=>o):new Error("OK"))}var G=e=>{const r=["assert","assert/strict","async_hooks","buffer","child_process","cluster","console","constants","crypto","dgram","diagnostics_channel","dns","dns/promises","domain","events","fs","fs/promises","http","http2","https","inspector","inspector/promises","module","net","os","path","path/posix","path/win32","perf_hooks","process","punycode","querystring","readline","repl","stream","stream/promises","stream/consumers","stream/web","string_decoder","sys","timers","timers/promises","tls","trace_events","tty","url","util","util/types","v8","vm","wasi","worker_threads","zlib"];if(e.indexOf("node:")===0||r.includes(e))return"node";const[t="",n=""]=e.split("/");return t.startsWith("@")?`${t}/${n}`:t},U=e=>{const r=new Map,t=new Map;let n=0,l=0;return a=>(n=0,l=0,o(a,0).then(d=>{var i,c;l>0&&((c=(i=e.delegate).finished)==null||c.call(i,t))}));function o(a,d){return m(this,null,function*(){var i,c,F,w,P;const M=J(e,r,a);M.forEach(s=>r.set(s.module,{state:"loading"}));const h=(yield Promise.all(M.map(s=>E(e,s.module,s.version)))).filter(s=>!("error"in s)),O=h.filter(s=>s.files.find(u=>$(u.name))),V=O.map(s=>j(s,`/node_modules/${s.moduleName}`)),C=h.filter(s=>!O.includes(s)),b=(yield Promise.all(C.map(s=>E(e,`@types/${y(s.moduleName)}`,"latest")))).filter(s=>!("error"in s)),W=b.map(s=>j(s,`/node_modules/@types/${y(s.moduleName).replace("types__","")}`)),_=V.concat(W).reduce((s,u)=>s.concat(u),[]);n+=_.length,_.length&&d===0&&((c=(i=e.delegate).started)==null||c.call(i));for(const s of h){let u=`/node_modules/${s.moduleName}`;b.includes(s)&&(u=`/node_modules/@types/${y(s.moduleName).replace("types__","")}`);const f=u+"/package.json",p=yield T(e,s.moduleName,s.version,"/package.json");typeof p=="string"?(t.set(f,p),(w=(F=e.delegate).receivedFile)==null||w.call(F,p,f)):(P=e.logger)==null||P.error(`Could not download package.json for ${s.moduleName}`)}yield Promise.all(_.map(s=>m(this,null,function*(){var u,f,p;const v=yield T(e,s.moduleName,s.moduleVersion,s.path);l++,v instanceof Error?(u=e.logger)==null||u.error(`Had an issue getting ${s.path} for ${s.moduleName}`):(t.set(s.vfsPath,v),(p=(f=e.delegate).receivedFile)==null||p.call(f,v,s.vfsPath),e.delegate.progress&&l%5===0&&e.delegate.progress(l,n),yield o(v,d+1))})))})}};function j(e,r){const t=[];for(const n of e.files)$(n.name)&&t.push({moduleName:e.moduleName,moduleVersion:e.version,vfsPath:`${r}${n.name}`,path:n.name});return t}var H=(e,r)=>{const t=e.preProcessFile(r),n=e.libMap||new Map;return t.referencedFiles.concat(t.importedFiles).concat(t.libReferenceDirectives).filter(o=>!$(o.fileName)).filter(o=>!n.has(o.fileName)).map(o=>{let a;if(!o.fileName.startsWith(".")){a="latest";const d=r.slice(o.end).split(`
`)[0];d.includes("// types:")&&(a=d.split("// types: ")[1].trim())}return{module:o.fileName,version:a}}).filter((o,a,d)=>d.findIndex(i=>i.module===o.module&&i.version===o.version)===a)};function J(e,r,t){return H(e.typescript,t).map(o=>x(k({},o),{module:G(o.module)})).filter(o=>!o.module.startsWith(".")).filter(o=>!r.has(o.module))}var E=(e,r,t)=>m(void 0,null,function*(){let n=t||"latest";if(n.split(".").length<2){const o=yield A(e,r,n);if(o instanceof Error)return{error:o,userFacingMessage:`Could not go from a tag to version on npm for ${r} - possible typo?`};const a=o.version;if(!a){const d=yield z(e,r);if(d instanceof Error)return{error:o,userFacingMessage:`Could not get versions on npm for ${r} - possible typo?`};const i=Object.entries(d.tags).join(", ");return{error:new Error("Could not find tag for module"),userFacingMessage:`Could not find a tag for ${r} called ${t}. Did find ${i}`}}n=a}const l=yield B(e,r,n);return l instanceof Error?{error:l,userFacingMessage:`Could not get the files for ${r}@${n}. Is it possibly a typo?`}:l});function y(e){return e.indexOf("@")===0&&e.indexOf("/")!==-1&&(e=e.substr(1).replace("/","__")),e}function $(e){return/\.d\.([^\.]+\.)?[cm]?ts$/i.test(e)}export{U as s};
