function O(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?g(Object(n),!0).forEach(function(r){O(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function y(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(r){return t.reduceRight(function(i,a){return a(i)},r)}}function c(e){return function t(){for(var n=this,r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return i.length>=e.length?e.apply(this,i):function(){for(var f=arguments.length,s=new Array(f),o=0;o<f;o++)s[o]=arguments[o];return t.apply(n,[].concat(i,s))}}}function h(e){return{}.toString.call(e).includes("Object")}function m(e){return!Object.keys(e).length}function l(e){return typeof e=="function"}function j(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function w(e,t){return h(t)||u("changeType"),Object.keys(t).some(function(n){return!j(e,n)})&&u("changeField"),t}function P(e){l(e)||u("selectorType")}function T(e){l(e)||h(e)||u("handlerType"),h(e)&&Object.values(e).some(function(t){return!l(t)})&&u("handlersType")}function S(e){e||u("initialIsRequired"),h(e)||u("initialType"),m(e)&&u("initialContent")}function E(e,t){throw new Error(e[t]||e.default)}var D={initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"},u=c(E)(D),p={changes:w,selector:P,handler:T,initial:S};function q(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};p.initial(e),p.handler(t);var n={current:e},r=c(I)(n,t),i=c(F)(n),a=c(p.changes)(e),f=c(C)(n);function s(){var d=arguments.length>0&&arguments[0]!==void 0?arguments[0]:function(b){return b};return p.selector(d),d(n.current)}function o(d){y(r,i,a,f)(d)}return[s,o]}function C(e,t){return l(t)?t(e.current):t}function F(e,t){return e.current=v(v({},e.current),t),t}function I(e,t,n){return l(t)?t(e.current):Object.keys(n).forEach(function(r){var i;return(i=t[r])===null||i===void 0?void 0:i.call(t,e.current[r])}),n}var R={create:q};export{R as i};
