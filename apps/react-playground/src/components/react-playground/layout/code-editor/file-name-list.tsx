import { useContext, useEffect, useState } from "react";
import { PlaygroundContext } from "../../playgroundContext";
import FileNameItem from "./file-name-item";
import {
  ENTRY_FILE_NAME,
  IMPORT_MAP_FILE_NAME,
  APP_COMPONENT_FILE_NAME,
} from "../../helper/file";
import styles from "./index.module.scss";

const READONLY_FILES = [
  ENTRY_FILE_NAME,
  IMPORT_MAP_FILE_NAME,
  APP_COMPONENT_FILE_NAME,
];

export function FileNameList() {
  const context = useContext(PlaygroundContext);
  const {
    files,
    removeFile,
    addFile,
    updateFileName,
    setSelectedFileName,
    selectedFileName,
  } = context;

  const [tabs, setTabs] = useState([""]);
  /** 新创建的文件是否正在编辑 */
  const [created, setCreated] = useState(false);

  useEffect(() => {
    setTabs(Object.keys(files));
  }, [files]);

  const handleFileClick = (fileName: string) => {
    setSelectedFileName(fileName);
  };

  const handleEditComplete = (newName: string, prevName: string) => {
    updateFileName(prevName, newName);
    setSelectedFileName(newName);
    setCreated(false);
  };

  const addTab = () => {
    const newFileName = `Comp${Math.random().toString().slice(2, 6)}.tsx`;
    addFile(newFileName);
    setSelectedFileName(newFileName);
    setCreated(true);
  };

  const handleRemove = (fileName: string) => {
    removeFile(fileName);
    setSelectedFileName(ENTRY_FILE_NAME);
  };

  return (
    <div className={styles.fileNameList}>
      {tabs.map((item, index) => (
        <FileNameItem
          key={item + index}
          value={item}
          readonly={READONLY_FILES.includes(item)}
          created={created && index === tabs.length - 1}
          selected={item === selectedFileName}
          onClick={() => handleFileClick(item)}
          onEditComplete={(value) => handleEditComplete(value, item)}
          onRemove={() => {
            handleRemove(item);
          }}
        />
      ))}
      <div className={styles.add} onClick={addTab}>
        +
      </div>
    </div>
  );
}

export default FileNameList;
