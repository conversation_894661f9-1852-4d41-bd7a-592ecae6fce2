# React 兼容性问题解决方案

## 问题描述

在生产环境中出现以下错误：
```
Uncaught (in promise) TypeError: Cannot read properties of undefined (reading 'memo')
```

## 问题原因

1. **版本不一致**: 开发环境使用本地安装的 React 19.1.1，生产环境使用 ESM CDN 的 React 19.1.0
2. **导出缺失**: React 19 中某些导出（如 `memo`）的导入方式发生变化
3. **CDN 延迟**: ESM CDN 可能存在版本更新延迟

## 解决方案

### 1. 更新 import-map.json

确保 CDN 版本与本地版本完全一致：

```json
{
  "imports": {
    "react": "https://esm.sh/react@19.1.1",
    "react-dom/client": "https://esm.sh/react-dom@19.1.1/client",
    "react/memo": "https://esm.sh/react@19.1.1/memo",
    "react/useState": "https://esm.sh/react@19.1.1/useState",
    "react/useEffect": "https://esm.sh/react@19.1.1/useEffect",
    "react/useContext": "https://esm.sh/react@19.1.1/useContext",
    "react/useRef": "https://esm.sh/react@19.1.1/useRef",
    "react/useCallback": "https://esm.sh/react@19.1.1/useCallback",
    "react/useMemo": "https://esm.sh/react@19.1.1/useMemo"
  }
}
```

### 2. 启用 Vite 依赖预构建

在 `vite.config.ts` 中启用 `optimizeDeps`：

```typescript
optimizeDeps: {
  include: [
    "react",
    "react-dom",
    // ... 其他依赖
  ],
}
```

### 3. 使用兼容性检查脚本

构建前运行兼容性检查：

```bash
npm run build:check
```

### 4. 运行时错误处理

添加了兼容性检查和错误处理机制，在生产环境中会显示友好的错误信息。

## 预防措施

1. **版本同步**: 确保 `package.json` 和 `import-map.json` 中的版本一致
2. **定期检查**: 定期运行兼容性检查脚本
3. **测试环境**: 在测试环境中验证生产构建
4. **回退机制**: 提供多个 CDN 源作为备选

## 常见问题

### Q: 为什么开发环境不报错？
A: 开发环境使用本地安装的 React，版本完全一致，不存在兼容性问题。

### Q: 如何快速定位问题？
A: 检查浏览器控制台的错误信息，查看 `import-map.json` 中的版本配置。

### Q: 可以降级 React 版本吗？
A: 可以，但需要同时更新 `package.json` 和 `import-map.json`，并测试所有功能。

## 相关文件

- `src/components/react-playground/helper/temp/import-map.json` - CDN 导入映射
- `scripts/check-compatibility.js` - 兼容性检查脚本
- `vite.config.ts` - Vite 构建配置
- `src/components/react-playground/layout/preview/compiler.ts` - 编译器兼容性检查
