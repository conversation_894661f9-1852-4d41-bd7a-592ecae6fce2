<template>
  <div class="default-welcome">
    <div class="welcome-icon">
      <svg
        width="80"
        height="80"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12 2L2 7L12 12L22 7L12 2Z"
          stroke="#64b687"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M2 17L12 22L22 17"
          stroke="#64b687"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M2 12L12 17L22 12"
          stroke="#64b687"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </div>
    <h2 class="welcome-title">欢迎使用 Copyer 项目集合</h2>
    <p class="welcome-description">
      这里是一个基于乾坤微前端架构的项目展示平台
    </p>
    <div class="welcome-features">
      <div class="feature-item">
        <div class="feature-icon">🚀</div>
        <span>微前端架构</span>
      </div>
      <div class="feature-item">
        <div class="feature-icon">⚡</div>
        <span>快速加载</span>
      </div>
      <div class="feature-item">
        <div class="feature-icon">🎯</div>
        <span>独立部署</span>
      </div>
    </div>
    <div class="welcome-action">
      <div class="action-arrow">👈</div>
      <span class="action-text">点击左侧项目开始体验</span>
    </div>
  </div>
</template>

<style scoped>
.default-welcome {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 500px;
  padding: 40px 20px;
  text-align: center;
  /* background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); */
  background-color: #fff;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.default-welcome::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(100, 182, 135, 0.1) 0%,
    transparent 70%
  );
  animation: float 6s ease-in-out infinite;
}

.welcome-icon {
  margin-bottom: 24px;
  animation: bounce 2s ease-in-out infinite;
  z-index: 1;
  position: relative;
}

.welcome-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
  z-index: 1;
  position: relative;
}

.welcome-description {
  font-size: 16px;
  color: #7f8c8d;
  margin-bottom: 32px;
  line-height: 1.6;
  max-width: 400px;
  z-index: 1;
  position: relative;
}

.welcome-features {
  display: flex;
  gap: 24px;
  margin-bottom: 40px;
  z-index: 1;
  position: relative;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  min-width: 100px;
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.feature-item span {
  font-size: 14px;
  color: #34495e;
  font-weight: 500;
}

.welcome-action {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: rgba(100, 182, 135, 0.1);
  border: 2px dashed #64b687;
  border-radius: 12px;
  z-index: 1;
  position: relative;
}

.action-arrow {
  font-size: 20px;
  animation: pulse 2s ease-in-out infinite;
}

.action-text {
  font-size: 16px;
  color: #64b687;
  font-weight: 500;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes float {
  0%,
  100% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
</style>
