import{r as c,j as s}from"./<EMAIL>";import{c as P}from"./react-dom@<EMAIL>";import{x as L}from"./allotment@1.20.4_react-dom@<EMAIL>";import{s as R,u as O,a as k,z as D}from"./<EMAIL>";import{J as $}from"./<EMAIL>";import{F as U}from"./<EMAIL>";import{c as B}from"./<EMAIL>";import{F as W}from"./@monaco-editor_react@4.7.0-rc.0_monaco-editor@0.52.2_react-dom@<EMAIL>";import"./<EMAIL>";import{s as J}from"./@typescript_ata@<EMAIL>";import{t as V}from"./<EMAIL>";import{c as I}from"./<EMAIL>";import{d as A,i as q}from"./<EMAIL>";import{b as H}from"./@<EMAIL>";import{h as M}from"./vite-plugin-qiankun@1.0.15_typescript@<EMAIL>";import"./<EMAIL>";import"./<EMAIL>";import"./@<EMAIL>";import"./<EMAIL>";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))o(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&o(l)}).observe(document,{childList:!0,subtree:!0});function n(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(r){if(r.ep)return;r.ep=!0;const i=n(r);fetch(r.href,i)}})();const C=t=>{const e=t.split(".").pop()||"";return["js","jsx"].includes(e)?"javascript":["ts","tsx"].includes(e)?"typescript":["json"].includes(e)?"json":["css"].includes(e)?"css":"javascript"};function K(t){try{const e=R(t),n=D(e,{level:9}),o=k(n,!0);return btoa(o)}catch(e){throw console.error("压缩失败:",e),new Error("数据压缩失败")}}function Q(t){try{if(!t||typeof t!="string")throw new Error("无效的 base64 字符串");const e=atob(t),n=R(e,!0);if(n.length===0)throw new Error("解压缩数据为空");const o=O(n);return k(o)}catch(e){throw console.error("解压缩失败:",e),new Error("数据解压缩失败: "+(e instanceof Error?e.message:"未知错误"))}}async function Y(t){const e=new $;Object.keys(t).forEach(o=>{e.file(o,t[o].value)});const n=await e.generateAsync({type:"blob"});U.saveAs(n,`code${Math.random().toString().slice(2,8)}.zip`)}const Z=`{
  "imports": {
    "react": "https://esm.sh/react@19.1.1",
    "react-dom/client": "https://esm.sh/react-dom@19.1.1/client",
    "react/memo": "https://esm.sh/react@19.1.1/memo",
    "react/useState": "https://esm.sh/react@19.1.1/useState",
    "react/useEffect": "https://esm.sh/react@19.1.1/useEffect",
    "react/useContext": "https://esm.sh/react@19.1.1/useContext",
    "react/useRef": "https://esm.sh/react@19.1.1/useRef",
    "react/useCallback": "https://esm.sh/react@19.1.1/useCallback",
    "react/useMemo": "https://esm.sh/react@19.1.1/useMemo"
  }
}
`,G=`:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 400;
  line-height: 1.5;
  color: rgb(255 255 255 / 87%);
  text-rendering: optimizelegibility;
  text-size-adjust: 100%;
  background-color: #242424;
  color-scheme: light dark;
  font-synthesis: none;
}

#root {
  max-width: 1280px;
  padding: 2rem;
  margin: 0 auto;
  text-align: center;
}

body {
  display: flex;
  min-width: 320px;
  min-height: 100vh;
  margin: 0;
  place-items: center;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  padding: 0.6em 1.2em;
  font-family: inherit;
  font-size: 1em;
  font-weight: 500;
  cursor: pointer;
  background-color: #1a1a1a;
  border: 1px solid transparent;
  border-radius: 8px;
  transition: border-color 0.25s;
}

button:hover {
  border-color: #646cff;
}

button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #fff;
  }

  button {
    background-color: #f9f9f9;
  }
}
.author {
  color: red;
}`,X=`import { useState } from "react";
import "./App.css";

function App() {
  const [count, setCount] = useState(0);

  return (
    <>
      <h1>Copyer</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          计数： {count}
        </button>
      </div>
    </>
  );
}

export default App;
`,ee=`import ReactDOM from "react-dom/client";

import App from "./App";

ReactDOM.createRoot(document.getElementById("root")!).render(<App />);
`,E="App.tsx",b="import-map.json",v="main.tsx",te={[v]:{name:v,language:C(v),value:ee},[E]:{name:E,language:C(E),value:X},"App.css":{name:"App.css",language:"css",value:G},[b]:{name:b,language:C(b),value:Z}},x=c.createContext({selectedFileName:"App.tsx"}),ne=t=>{const{children:e}=t,[n,o]=c.useState(p()||te),[r,i]=c.useState("light"),[l,d]=c.useState("App.tsx"),f=u=>{n[u]={name:u,language:C(u),value:""},o({...n})},g=u=>{delete n[u],o({...n})},h=(u,m)=>{if(!n[u]||m===void 0||m===null||u===m)return;const{[u]:_,...a}=n,y={[m]:{..._,language:C(m),name:m}};o({...a,...y})};function p(){let u;try{const m=Q(window.location.hash.slice(1));u=JSON.parse(m)}catch(m){console.error(m)}return u}return c.useEffect(()=>{const u=K(JSON.stringify(n));window.location.hash=u},[n]),s.jsx(x.Provider,{value:{theme:r,setTheme:i,files:n,selectedFileName:l,setSelectedFileName:d,setFiles:o,addFile:f,removeFile:g,updateFileName:h},children:e})},se="_header_16kaf_1",oe="_logo_16kaf_13",re="_link_16kaf_23",S={header:se,logo:oe,link:re},ie="data:image/svg+xml,%3csvg%20t='1755572492147'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='11955'%20width='256'%20height='256'%3e%3cpath%20d='M832%20768v64H192v-64h-64v128h768V768z%20m-9.376-329.376l-45.248-45.248L544%20626.752V128h-64v498.752L246.624%20393.376l-45.248%2045.248L512%20749.248z'%20fill='%23ffffff'%20p-id='11956'%3e%3c/path%3e%3c/svg%3e",ce="data:image/svg+xml,%3csvg%20t='1755572492147'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='11955'%20width='256'%20height='256'%3e%3cpath%20d='M832%20768v64H192v-64h-64v128h768V768z%20m-9.376-329.376l-45.248-45.248L544%20626.752V128h-64v498.752L246.624%20393.376l-45.248%2045.248L512%20749.248z'%20fill='%23181818'%20p-id='11956'%3e%3c/path%3e%3c/svg%3e",ae="data:image/svg+xml,%3csvg%20t='1755572446398'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='10074'%20width='256'%20height='256'%3e%3cpath%20d='M778.602987%20653.725862c-43.735084%200-82.508155%2020.923542-107.114576%2053.234435l-283.026028-168.531368c3.213181-12.398378%205.100158-25.314549%205.100158-38.715767%200-3.89061-0.302899-7.686053-0.573051-11.494799l283.161105-170.612773c24.713868%2028.935006%2061.412698%2047.344285%20102.452393%2047.344285%2074.43427%200%20134.771473-60.338227%20134.771473-134.76738%200-74.42506-60.338227-134.763286-134.771473-134.763286-74.424037%200-134.759193%2060.334133-134.759193%20134.763286%200%2014.767332%202.445702%2028.916587%206.837732%2042.222637L369.926214%20417.98579c-27.26497-43.37795-75.391061-72.290443-130.376373-72.290443-85.053118%200-154.017816%2068.959581-154.017816%20154.017816s68.963675%20154.012699%20154.017816%20154.012699c45.589314%200%2086.426395-19.925818%20114.624621-51.3976L648.347364%20754.476497c-2.829442%2010.872628-4.490268%2022.23235-4.490268%2034.012651%200%2074.43427%2060.34232%20134.76738%20134.76738%20134.76738%2074.429153%200%20134.763286-60.33311%20134.763286-134.76738C913.388786%20714.059995%20853.037257%20653.725862%20778.602987%20653.725862L778.602987%20653.725862%20778.602987%20653.725862zM778.602987%20653.725862'%20fill='%23ffffff'%20p-id='10075'%3e%3c/path%3e%3c/svg%3e",le="data:image/svg+xml,%3csvg%20t='1755572404877'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='9912'%20width='256'%20height='256'%3e%3cpath%20d='M778.602987%20653.725862c-43.735084%200-82.508155%2020.923542-107.114576%2053.234435l-283.026028-168.531368c3.213181-12.398378%205.100158-25.314549%205.100158-38.715767%200-3.89061-0.302899-7.686053-0.573051-11.494799l283.161105-170.612773c24.713868%2028.935006%2061.412698%2047.344285%20102.452393%2047.344285%2074.43427%200%20134.771473-60.338227%20134.771473-134.76738%200-74.42506-60.338227-134.763286-134.771473-134.763286-74.424037%200-134.759193%2060.334133-134.759193%20134.763286%200%2014.767332%202.445702%2028.916587%206.837732%2042.222637L369.926214%20417.98579c-27.26497-43.37795-75.391061-72.290443-130.376373-72.290443-85.053118%200-154.017816%2068.959581-154.017816%20154.017816s68.963675%20154.012699%20154.017816%20154.012699c45.589314%200%2086.426395-19.925818%20114.624621-51.3976L648.347364%20754.476497c-2.829442%2010.872628-4.490268%2022.23235-4.490268%2034.012651%200%2074.43427%2060.34232%20134.76738%20134.76738%20134.76738%2074.429153%200%20134.763286-60.33311%20134.763286-134.76738C913.388786%20714.059995%20853.037257%20653.725862%20778.602987%20653.725862L778.602987%20653.725862%20778.602987%20653.725862zM778.602987%20653.725862'%20fill='%23272636'%20p-id='9913'%3e%3c/path%3e%3c/svg%3e",de="data:image/svg+xml,%3csvg%20t='1755570835589'%20class='icon'%20viewBox='0%200%201024%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='8866'%20width='256'%20height='256'%3e%3cpath%20d='M508.475476%20777.927066a262.879%20262.879%200%201%200%2014.927684-525.546038%20262.879%20262.879%200%201%200-14.927684%20525.546038Z'%20p-id='8867'%20fill='%23ffffff'%3e%3c/path%3e%3cpath%20d='M512.119%20213.154c17.673%200%2032-14.327%2032-32V95.896c0-17.673-14.327-32-32-32-17.673%200-32%2014.327-32%2032v85.258c0%2017.673%2014.327%2032%2032%2032zM743.779%20308.938c8.186%200.232%2016.461-2.658%2022.884-8.727l61.973-58.553c12.845-12.137%2013.42-32.391%201.283-45.237-12.139-12.846-32.391-13.421-45.237-1.283l-61.973%2058.553c-12.845%2012.137-13.42%2032.391-1.283%2045.237%206.069%206.422%2014.167%209.777%2022.353%2010.01zM936.04%20487.929l-85.224-2.417c-17.666-0.501-32.393%2013.414-32.894%2031.08-0.501%2017.666%2013.414%2032.394%2031.08%2032.895l85.224%202.417c17.666%200.501%2032.393-13.415%2032.894-31.08%200.501-17.667-13.414-32.394-31.08-32.895zM773.672%20730.999c-12.137-12.846-32.391-13.42-45.237-1.284-12.846%2012.138-13.421%2032.391-1.284%2045.237l58.552%2061.972c6.069%206.424%2014.166%209.779%2022.353%2010.011%208.185%200.232%2016.462-2.659%2022.884-8.727%2012.846-12.138%2013.421-32.391%201.284-45.237l-58.552-61.972zM514.455%20817.14c-17.666-0.501-32.393%2013.414-32.894%2031.08l-2.417%2085.224c-0.501%2017.666%2013.414%2032.393%2031.08%2032.894%2017.666%200.501%2032.393-13.414%2032.894-31.08l2.417-85.224c0.501-17.665-13.414-32.393-31.08-32.894zM256.094%20726.369l-61.972%2058.553c-12.846%2012.138-13.42%2032.391-1.283%2045.237%206.069%206.425%2014.165%209.779%2022.353%2010.011%208.185%200.232%2016.461-2.659%2022.884-8.727l61.972-58.553c12.846-12.138%2013.42-32.391%201.283-45.237-12.138-12.848-32.391-13.42-45.237-1.284zM213.906%20513.673c0.501-17.666-13.414-32.393-31.08-32.894l-85.224-2.417c-17.666-0.501-32.393%2013.414-32.894%2031.08-0.501%2017.666%2013.414%2032.393%2031.08%2032.894l85.224%202.417c17.665%200.501%2032.393-13.414%2032.894-31.08zM258.157%20299.266c6.069%206.423%2014.166%209.778%2022.353%2010.01%208.186%200.232%2016.461-2.658%2022.884-8.727%2012.846-12.137%2013.42-32.39%201.283-45.237l-58.553-61.972c-12.138-12.846-32.39-13.421-45.237-1.283-12.846%2012.137-13.42%2032.391-1.283%2045.237l58.553%2061.972z'%20p-id='8868'%20fill='%23ffffff'%3e%3c/path%3e%3c/svg%3e",ue="data:image/svg+xml,%3csvg%20t='1755569259782'%20class='icon'%20viewBox='0%200%201066%201024'%20version='1.1'%20xmlns='http://www.w3.org/2000/svg'%20p-id='6968'%20width='256'%20height='256'%3e%3cpath%20d='M685.653333%20142.677333l-60.928%2033.322667%2060.928%2033.322667%2033.322667%2060.928%2033.322667-60.928%2060.928-33.322667-60.928-33.322667-33.28-60.928-33.365334%2060.928zM94.976%20512c0-235.648%20191.018667-426.666667%20426.666667-426.666667h73.984l-37.034667%2064c-24.704%2042.666667-36.949333%2093.397333-36.949333%20149.333334a298.666667%20298.666667%200%200%200%20356.181333%20293.12l71.765333-13.952-23.168%2069.376C869.973333%20816.512%20710.101333%20938.666667%20521.642667%20938.666667c-235.648%200-426.666667-191.018667-426.666667-426.666667z%20m789.333333-238.250667l38.997334%2071.253334L994.56%20384l-71.253333%2038.997333-38.997334%2071.253334-38.954666-71.253334L774.101333%20384l71.253334-38.997333%2038.954666-71.253334z'%20fill='%23000000'%20p-id='6969'%3e%3c/path%3e%3c/svg%3e",pe=new URL("/subs/react-playground/svg/logo-CqE24J1b.svg",import.meta.url).href,me=()=>{const{theme:t,setTheme:e,files:n}=c.useContext(x);return s.jsxs("div",{className:S.header,children:[s.jsxs("div",{className:S.logo,children:[s.jsx("img",{src:pe,alt:"logo"}),s.jsx("div",{className:"title",children:"React PlayGround"})]}),s.jsxs("div",{className:S.link,children:[s.jsx("img",{className:"icon",src:t!=="light"?de:ue,alt:"theme",onClick:()=>e(t==="light"?"dark":"light")}),s.jsx("img",{className:"icon",src:t!=="light"?ie:ce,alt:"download",onClick:async()=>{await Y(n)}}),s.jsx("img",{className:"icon",src:t!=="light"?ae:le,alt:"share",onClick:()=>{B(window.location.href),alert("分享链接已复制。")}})]})]})};function fe(t){return J({typescript:V,logger:console,delegate:{receivedFile:(n,o)=>{t(n,o)}}})}function he(t){const{file:e,onChange:n,options:o={}}=t,{theme:r}=c.useContext(x),i=(l,d)=>{l.addCommand(d.KeyMod.CtrlCmd|d.KeyCode.KeyK,()=>{l.getAction("editor.action.formatDocument")?.run()});const f=fe((g,h)=>{d.languages.typescript.typescriptDefaults.addExtraLib(g,`file://${h}`)});d.languages.typescript.typescriptDefaults.setCompilerOptions({jsx:d.languages.typescript.JsxEmit.Preserve,esModuleInterop:!0}),l.onDidChangeModelContent(()=>{f(l.getValue())}),f(l.getValue())};return s.jsx(W,{height:"100%",path:e.name,language:e.language,onMount:i,value:e.value,onChange:n,theme:`vs-${r}`,options:{fontSize:14,scrollBeyondLastLine:!1,minimap:{enabled:!1},scrollbar:{verticalScrollbarSize:6,horizontalScrollbarSize:6},...o}})}const ge="_CodeEditor_1uyzt_1",ve="_fileNameList_1uyzt_11",xe="_tabItem_1uyzt_32",ye="_selectTabItem_1uyzt_41",be="_tabsItemInput_1uyzt_46",we="_add_1uyzt_57",w={CodeEditor:ge,fileNameList:ve,tabItem:xe,selectTabItem:ye,tabsItemInput:be,add:we},je=t=>{const{value:e,selected:n,onClick:o,onEditComplete:r,created:i,onRemove:l,readonly:d}=t,[f,g]=c.useState(e),[h,p]=c.useState(i),u=c.useRef(null),m=()=>{d||(p(!0),setTimeout(()=>{u.current?.focus()},0))},_=a=>{a.stopPropagation(),confirm("确定删除吗？")&&l?.()};return s.jsx("div",{onClick:o,className:I(w.tabItem,{[w.selectTabItem]:n}),children:h?s.jsx("input",{ref:u,value:f,className:w.tabsItemInput,onChange:a=>g(a.target.value),onBlur:()=>{p(!1),r?.(f)}}):s.jsxs(s.Fragment,{children:[s.jsx("span",{onDoubleClick:m,children:f}),!d&&s.jsx("span",{style:{marginLeft:5,display:"flex"},onClick:_,children:s.jsxs("svg",{width:"12",height:"12",viewBox:"0 0 24 24",children:[s.jsx("line",{stroke:"#999",x1:"18",y1:"6",x2:"6",y2:"18"}),s.jsx("line",{stroke:"#999",x1:"6",y1:"6",x2:"18",y2:"18"})]})})]})})},_e=[v,b,E];function Ce(){const t=c.useContext(x),{files:e,removeFile:n,addFile:o,updateFileName:r,setSelectedFileName:i,selectedFileName:l}=t,[d,f]=c.useState([""]),[g,h]=c.useState(!1);c.useEffect(()=>{f(Object.keys(e))},[e]);const p=a=>{i(a)},u=(a,y)=>{r(y,a),i(a),h(!1)},m=()=>{const a=`Comp${Math.random().toString().slice(2,6)}.tsx`;o(a),i(a),h(!0)},_=a=>{n(a),i(v)};return s.jsxs("div",{className:w.fileNameList,children:[d.map((a,y)=>s.jsx(je,{value:a,readonly:_e.includes(a),created:g&&y===d.length-1,selected:a===l,onClick:()=>p(a),onEditComplete:T=>u(T,a),onRemove:()=>{_(a)}},a+y)),s.jsx("div",{className:w.add,onClick:m,children:"+"})]})}const Ee=()=>{const t=c.useContext(x),{files:e,selectedFileName:n,setFiles:o}=t,r=e[n];function i(l){q(l)||(e[n].value=l,o({...e}))}return s.jsxs("div",{className:w.CodeEditor,children:[s.jsx("div",{className:"file-name-list",children:s.jsx(Ce,{})}),s.jsx("div",{className:"editor",children:s.jsx(he,{file:r,onChange:A(i,500)})})]})},Le="_msg_v5z09_1",Se="_error_v5z09_17",ze="_warn_v5z09_21",Me="_dismiss_v5z09_33",z={msg:Le,error:Se,warn:ze,dismiss:Me},Ne=t=>{const{type:e,content:n}=t,[o,r]=c.useState(!1);return c.useEffect(()=>{r(!!n)},[n]),o?s.jsxs("div",{className:I(z.msg,z[e]),children:[s.jsx("pre",{dangerouslySetInnerHTML:{__html:n}}),s.jsx("button",{className:z.dismiss,onClick:()=>r(!1),children:"✕"})]}):null},Re=`<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Preview</title>
  </head>
  <body>
    <script>
      window.addEventListener("error", (e) => {
        // 监听到错误，传递给父元素
        window.parent.postMessage({ type: "error", message: e.message }, "*");
      });
    <\/script>
    <script type="importmap"><\/script>
    <script type="module" id="appSrc"><\/script>
    <div id="root"></div>
  </body>
</html>
`,ke=(t,e)=>{let n=e;const o=/import\s+React\b/g;return(t.endsWith(".jsx")||t.endsWith(".tsx"))&&!o.test(e)&&(n=`import React from 'react';
${e}`),n},F=(t,e,n)=>{let o="";try{const r=ke(t,e);o=H.transform(r,{presets:["react","typescript"],filename:t,plugins:[Te(n)],retainLines:!0}).code}catch(r){console.error("编译出错",r)}return o},Ie=t=>{const e=t[v];return F(v,e.value,t)};function Ae(t){const e=`export default ${t.value}`;return URL.createObjectURL(new Blob([e],{type:"application/json"}))}function Fe(t){const n=`(() => {
  const styleSheet = document.createElement("style");
  styleSheet.setAttribute("id", 'style_${new Date().getTime()}_${t.name}');
  document.head.appendChild(styleSheet);

  const styles = document.createTextNode(\`${t.value}\`);
  styleSheet.innerHTML = "";
  styleSheet.appendChild(styles);
  })()`;return URL.createObjectURL(new Blob([n],{type:"text/javascript"}))}function Te(t){return{visitor:{ImportDeclaration(e){const n=e.node.source.value;if(n.startsWith(".")){const o=Pe(t,n);if(!o)return;o.name.endsWith(".css")?e.node.source.value=Fe(o):o.name.endsWith(".json")?e.node.source.value=Ae(o):e.node.source.value=URL.createObjectURL(new Blob([F(o.name,o.value,t)],{type:"application/javascript"}))}}}}}function Pe(t,e){let n=e.split("./").pop()||"";if(!n.includes(".")){const o=Object.keys(t).filter(r=>r.endsWith(".ts")||r.endsWith(".tsx")||r.endsWith(".js")||r.endsWith(".jsx")).find(r=>r.split(".").includes(n));o&&(n=o)}return t[n]}function Oe(t){try{const e=t["import-map.json"];if(!e)return!1;const o=JSON.parse(e.value).imports?.react;return o&&o.includes("19.1.1")}catch(e){return console.error("React 兼容性检查失败:",e),!1}}function De(){const{files:t}=c.useContext(x),[e,n]=c.useState(""),[o,r]=c.useState(""),[i,l]=c.useState(""),[d,f]=c.useState("");c.useEffect(A(()=>{try{if(!Oe(t)){f("React 版本兼容性检查失败，请确保使用正确的版本");return}f(""),n(Ie(t))}catch(p){console.error("编译失败:",p),l(`编译失败: ${p instanceof Error?p.message:String(p)}`)}},500),[t]);const g=()=>{const p=Re.replace('<script type="importmap"><\/script>',`<script type="importmap">${t[b].value}<\/script>`).replace('<script type="module" id="appSrc"><\/script>',`<script type="module" id="appSrc">${e}<\/script>`);return URL.createObjectURL(new Blob([p],{type:"text/html"}))};c.useEffect(()=>{e&&!d&&r(g())},[t[b].value,e,d]);const h=p=>{p.data.type==="error"&&l(p.data.message)};return c.useEffect(()=>(window.addEventListener("message",h),()=>{window.removeEventListener("message",h)}),[]),s.jsxs("div",{style:{height:"100%",position:"relative"},children:[o&&!d?s.jsx("iframe",{src:o,style:{width:"100%",height:"100%",padding:0,border:"none"}}):d?s.jsxs("div",{style:{padding:"20px",textAlign:"center",color:"#ff4d4f",backgroundColor:"#fff2f0",border:"1px solid #ffccc7",borderRadius:"6px",margin:"20px"},children:[s.jsx("h3",{children:"兼容性错误"}),s.jsx("p",{children:d}),s.jsx("p",{children:"请检查 import-map.json 中的 React 版本配置"})]}):s.jsx("div",{children:"编译中..."}),s.jsx(Ne,{type:"error",content:i})]})}function $e(){const{theme:t}=c.useContext(x);return s.jsxs("div",{className:`react_playground_${t}`,style:{height:"100vh"},children:[s.jsx(me,{}),s.jsx("div",{style:{height:"calc(100vh - 60px)"},children:s.jsxs(L,{defaultSizes:[100,100],children:[s.jsx(L.Pane,{minSize:500,children:s.jsx(Ee,{})}),s.jsx(L.Pane,{minSize:0,children:s.jsx(De,{})})]})})]})}function Ue(){return s.jsx(ne,{children:s.jsx($e,{})})}function Be(){return s.jsx(Ue,{})}let j=null;function N(t={}){const{container:e}=t,n=e?e.querySelector("#root"):document.querySelector("#root");j||(j=P.createRoot(n)),j.render(s.jsx(Be,{})),setTimeout(()=>{(e?e.ownerDocument.body:document.body).classList.add("app-loaded")},100)}function We(){if(!M.qiankunWindow.__POWERED_BY_QIANKUN__){console.log("%c 独立渲染","color: red; font-size: 20px;"),N();return}M.renderWithQiankun({mount(t){console.log("%c qiankun 渲染","color: red; font-size: 20px;"),console.log("Qiankun mount",t),N(t)},bootstrap(){console.log("Qiankun bootstrap")},unmount(t){console.log("Qiankun unmount",t),j&&(j.unmount(),j=null)},update(t){console.log("Qiankun update",t)}})}We();
