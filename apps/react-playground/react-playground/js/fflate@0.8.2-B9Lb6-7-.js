var b=Uint8Array,N=Uint16Array,yr=Int32Array,or=new b([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),ur=new b([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),Cr=new b([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),sr=function(r,a){for(var e=new N(31),n=0;n<31;++n)e[n]=a+=1<<r[n-1];for(var v=new yr(e[30]),n=1;n<30;++n)for(var f=e[n];f<e[n+1];++f)v[f]=f-e[n]<<5|n;return{b:e,r:v}},Er=sr(or,2),Ur=Er.b,xr=Er.r;Ur[28]=258,xr[258]=28;var qr=sr(ur,0),Dr=qr.b,Sr=qr.r,Mr=new N(32768);for(var C=0;C<32768;++C){var j=(C&43690)>>1|(C&21845)<<1;j=(j&52428)>>2|(j&13107)<<2,j=(j&61680)>>4|(j&3855)<<4,Mr[C]=((j&65280)>>8|(j&255)<<8)>>1}var X=(function(r,a,e){for(var n=r.length,v=0,f=new N(a);v<n;++v)r[v]&&++f[r[v]-1];var o=new N(a);for(v=1;v<a;++v)o[v]=o[v-1]+f[v-1]<<1;var u;if(e){u=new N(1<<a);var c=15-a;for(v=0;v<n;++v)if(r[v])for(var t=v<<4|r[v],l=a-r[v],i=o[r[v]-1]++<<l,w=i|(1<<l)-1;i<=w;++i)u[Mr[i]>>c]=t}else for(u=new N(n),v=0;v<n;++v)r[v]&&(u[v]=Mr[o[r[v]-1]++]>>15-r[v]);return u}),L=new b(288);for(var C=0;C<144;++C)L[C]=8;for(var C=144;C<256;++C)L[C]=9;for(var C=256;C<280;++C)L[C]=7;for(var C=280;C<288;++C)L[C]=8;var fr=new b(32);for(var C=0;C<32;++C)fr[C]=5;var Gr=X(L,9,0),Hr=X(L,9,1),Jr=X(fr,5,0),Kr=X(fr,5,1),wr=function(r){for(var a=r[0],e=1;e<r.length;++e)r[e]>a&&(a=r[e]);return a},V=function(r,a,e){var n=a/8|0;return(r[n]|r[n+1]<<8)>>(a&7)&e},gr=function(r,a){var e=a/8|0;return(r[e]|r[e+1]<<8|r[e+2]<<16)>>(a&7)},br=function(r){return(r+7)/8|0},hr=function(r,a,e){return(a==null||a<0)&&(a=0),(e==null||e>r.length)&&(e=r.length),new b(r.subarray(a,e))},Nr=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],Q=function(r,a,e){var n=new Error(a||Nr[r]);if(n.code=r,Error.captureStackTrace&&Error.captureStackTrace(n,Q),!e)throw n;return n},Pr=function(r,a,e,n){var v=r.length,f=0;if(!v||a.f&&!a.l)return e||new b(0);var o=!e,u=o||a.i!=2,c=a.i;o&&(e=new b(v*3));var t=function(ar){var er=e.length;if(ar>er){var d=new b(Math.max(er*2,ar));d.set(e),e=d}},l=a.f||0,i=a.p||0,w=a.b||0,x=a.l,E=a.d,M=a.m,O=a.n,R=v*8;do{if(!x){l=V(r,i,1);var G=V(r,i+1,3);if(i+=3,G)if(G==1)x=Hr,E=Kr,M=9,O=5;else if(G==2){var I=V(r,i,31)+257,z=V(r,i+10,15)+4,g=I+V(r,i+5,31)+1;i+=14;for(var h=new b(g),A=new b(19),y=0;y<z;++y)A[Cr[y]]=V(r,i+y*3,7);i+=z*3;for(var q=wr(A),$=(1<<q)-1,H=X(A,q,1),y=0;y<g;){var m=H[V(r,i,$)];i+=m&15;var S=m>>4;if(S<16)h[y++]=S;else{var T=0,F=0;for(S==16?(F=3+V(r,i,3),i+=2,T=h[y-1]):S==17?(F=3+V(r,i,7),i+=3):S==18&&(F=11+V(r,i,127),i+=7);F--;)h[y++]=T}}var D=h.subarray(0,I),s=h.subarray(I);M=wr(D),O=wr(s),x=X(D,M,1),E=X(s,O,1)}else Q(1);else{var S=br(i)+4,B=r[S-4]|r[S-3]<<8,U=S+B;if(U>v){c&&Q(0);break}u&&t(w+B),e.set(r.subarray(S,U),w),a.b=w+=B,a.p=i=U*8,a.f=l;continue}if(i>R){c&&Q(0);break}}u&&t(w+131072);for(var rr=(1<<M)-1,P=(1<<O)-1,Y=i;;Y=i){var T=x[gr(r,i)&rr],J=T>>4;if(i+=T&15,i>R){c&&Q(0);break}if(T||Q(2),J<256)e[w++]=J;else if(J==256){Y=i,x=null;break}else{var K=J-254;if(J>264){var y=J-257,k=or[y];K=V(r,i,(1<<k)-1)+Ur[y],i+=k}var W=E[gr(r,i)&P],_=W>>4;W||Q(3),i+=W&15;var s=Dr[_];if(_>3){var k=ur[_];s+=gr(r,i)&(1<<k)-1,i+=k}if(i>R){c&&Q(0);break}u&&t(w+131072);var p=w+K;if(w<s){var lr=f-s,ir=Math.min(s,p);for(lr+w<0&&Q(3);w<ir;++w)e[w]=n[lr+w]}for(;w<p;++w)e[w]=e[w-s]}}a.l=x,a.p=Y,a.b=w,a.f=l,x&&(l=1,a.m=M,a.d=E,a.n=O)}while(!l);return w!=e.length&&o?hr(e,0,w):e.subarray(0,w)},Z=function(r,a,e){e<<=a&7;var n=a/8|0;r[n]|=e,r[n+1]|=e>>8},nr=function(r,a,e){e<<=a&7;var n=a/8|0;r[n]|=e,r[n+1]|=e>>8,r[n+2]|=e>>16},cr=function(r,a){for(var e=[],n=0;n<r.length;++n)r[n]&&e.push({s:n,f:r[n]});var v=e.length,f=e.slice();if(!v)return{t:Ir,l:0};if(v==1){var o=new b(e[0].s+1);return o[e[0].s]=1,{t:o,l:1}}e.sort(function(U,I){return U.f-I.f}),e.push({s:-1,f:25001});var u=e[0],c=e[1],t=0,l=1,i=2;for(e[0]={s:-1,f:u.f+c.f,l:u,r:c};l!=v-1;)u=e[e[t].f<e[i].f?t++:i++],c=e[t!=l&&e[t].f<e[i].f?t++:i++],e[l++]={s:-1,f:u.f+c.f,l:u,r:c};for(var w=f[0].s,n=1;n<v;++n)f[n].s>w&&(w=f[n].s);var x=new N(w+1),E=Fr(e[l-1],x,0);if(E>a){var n=0,M=0,O=E-a,R=1<<O;for(f.sort(function(I,z){return x[z.s]-x[I.s]||I.f-z.f});n<v;++n){var G=f[n].s;if(x[G]>a)M+=R-(1<<E-x[G]),x[G]=a;else break}for(M>>=O;M>0;){var S=f[n].s;x[S]<a?M-=1<<a-x[S]++-1:++n}for(;n>=0&&M;--n){var B=f[n].s;x[B]==a&&(--x[B],++M)}E=a}return{t:new b(x),l:E}},Fr=function(r,a,e){return r.s==-1?Math.max(Fr(r.l,a,e+1),Fr(r.r,a,e+1)):a[r.s]=e},zr=function(r){for(var a=r.length;a&&!r[--a];);for(var e=new N(++a),n=0,v=r[0],f=1,o=function(c){e[n++]=c},u=1;u<=a;++u)if(r[u]==v&&u!=a)++f;else{if(!v&&f>2){for(;f>138;f-=138)o(32754);f>2&&(o(f>10?f-11<<5|28690:f-3<<5|12305),f=0)}else if(f>3){for(o(v),--f;f>6;f-=6)o(8304);f>2&&(o(f-3<<5|8208),f=0)}for(;f--;)o(v);f=1,v=r[u]}return{c:e.subarray(0,n),n:a}},vr=function(r,a){for(var e=0,n=0;n<a.length;++n)e+=r[n]*a[n];return e},Br=function(r,a,e){var n=e.length,v=br(a+2);r[v]=n&255,r[v+1]=n>>8,r[v+2]=r[v]^255,r[v+3]=r[v+1]^255;for(var f=0;f<n;++f)r[v+f+4]=e[f];return(v+4+n)*8},Ar=function(r,a,e,n,v,f,o,u,c,t,l){Z(a,l++,e),++v[256];for(var i=cr(v,15),w=i.t,x=i.l,E=cr(f,15),M=E.t,O=E.l,R=zr(w),G=R.c,S=R.n,B=zr(M),U=B.c,I=B.n,z=new N(19),g=0;g<G.length;++g)++z[G[g]&31];for(var g=0;g<U.length;++g)++z[U[g]&31];for(var h=cr(z,7),A=h.t,y=h.l,q=19;q>4&&!A[Cr[q-1]];--q);var $=t+5<<3,H=vr(v,L)+vr(f,fr)+o,m=vr(v,w)+vr(f,M)+o+14+3*q+vr(z,A)+2*z[16]+3*z[17]+7*z[18];if(c>=0&&$<=H&&$<=m)return Br(a,l,r.subarray(c,c+t));var T,F,D,s;if(Z(a,l,1+(m<H)),l+=2,m<H){T=X(w,x,0),F=w,D=X(M,O,0),s=M;var rr=X(A,y,0);Z(a,l,S-257),Z(a,l+5,I-1),Z(a,l+10,q-4),l+=14;for(var g=0;g<q;++g)Z(a,l+3*g,A[Cr[g]]);l+=3*q;for(var P=[G,U],Y=0;Y<2;++Y)for(var J=P[Y],g=0;g<J.length;++g){var K=J[g]&31;Z(a,l,rr[K]),l+=A[K],K>15&&(Z(a,l,J[g]>>5&127),l+=J[g]>>12)}}else T=Gr,F=L,D=Jr,s=fr;for(var g=0;g<u;++g){var k=n[g];if(k>255){var K=k>>18&31;nr(a,l,T[K+257]),l+=F[K+257],K>7&&(Z(a,l,k>>23&31),l+=or[K]);var W=k&31;nr(a,l,D[W]),l+=s[W],W>3&&(nr(a,l,k>>5&8191),l+=ur[W])}else nr(a,l,T[k]),l+=F[k]}return nr(a,l,T[256]),l+F[256]},Qr=new yr([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Ir=new b(0),Rr=function(r,a,e,n,v,f){var o=f.z||r.length,u=new b(n+o+5*(1+Math.ceil(o/7e3))+v),c=u.subarray(n,u.length-v),t=f.l,l=(f.r||0)&7;if(a){l&&(c[0]=f.r>>3);for(var i=Qr[a-1],w=i>>13,x=i&8191,E=(1<<e)-1,M=f.p||new N(32768),O=f.h||new N(E+1),R=Math.ceil(e/3),G=2*R,S=function(tr){return(r[tr]^r[tr+1]<<R^r[tr+2]<<G)&E},B=new yr(25e3),U=new N(288),I=new N(32),z=0,g=0,h=f.i||0,A=0,y=f.w||0,q=0;h+2<o;++h){var $=S(h),H=h&32767,m=O[$];if(M[H]=m,O[$]=H,y<=h){var T=o-h;if((z>7e3||A>24576)&&(T>423||!t)){l=Ar(r,c,0,B,U,I,g,A,q,h-q,l),A=z=g=0,q=h;for(var F=0;F<286;++F)U[F]=0;for(var F=0;F<30;++F)I[F]=0}var D=2,s=0,rr=x,P=H-m&32767;if(T>2&&$==S(h-P))for(var Y=Math.min(w,T)-1,J=Math.min(32767,h),K=Math.min(258,T);P<=J&&--rr&&H!=m;){if(r[h+D]==r[h+D-P]){for(var k=0;k<K&&r[h+k]==r[h+k-P];++k);if(k>D){if(D=k,s=P,k>Y)break;for(var W=Math.min(P,k-2),_=0,F=0;F<W;++F){var p=h-P+F&32767,lr=M[p],ir=p-lr&32767;ir>_&&(_=ir,m=p)}}}H=m,m=M[H],P+=H-m&32767}if(s){B[A++]=268435456|xr[D]<<18|Sr[s];var ar=xr[D]&31,er=Sr[s]&31;g+=or[ar]+ur[er],++U[257+ar],++I[er],y=h+D,++z}else B[A++]=r[h],++U[r[h]]}}for(h=Math.max(h,y);h<o;++h)B[A++]=r[h],++U[r[h]];l=Ar(r,c,t,B,U,I,g,A,q,h-q,l),t||(f.r=l&7|c[l/8|0]<<3,l-=7,f.h=O,f.p=M,f.i=h,f.w=y)}else{for(var h=f.w||0;h<o+t;h+=65535){var d=h+65535;d>=o&&(c[l/8|0]=t,d=o),l=Br(c,l+1,r.subarray(h,d))}f.i=o}return hr(u,0,n+br(l)+v)},Or=function(){var r=1,a=0;return{p:function(e){for(var n=r,v=a,f=e.length|0,o=0;o!=f;){for(var u=Math.min(o+2655,f);o<u;++o)v+=n+=e[o];n=(n&65535)+15*(n>>16),v=(v&65535)+15*(v>>16)}r=n,a=v},d:function(){return r%=65521,a%=65521,(r&255)<<24|(r&65280)<<8|(a&255)<<8|a>>8}}},Vr=function(r,a,e,n,v){if(!v&&(v={l:1},a.dictionary)){var f=a.dictionary.subarray(-32768),o=new b(f.length+r.length);o.set(f),o.set(r,f.length),r=o,v.w=f.length}return Rr(r,a.level==null?6:a.level,a.mem==null?v.l?Math.ceil(Math.max(8,Math.min(13,Math.log(r.length)))*1.5):20:12+a.mem,e,n,v)},mr=function(r,a,e){for(;e;++a)r[a]=e,e>>>=8},Wr=function(r,a){var e=a.level,n=e==0?0:e<6?1:e==9?3:2;if(r[0]=120,r[1]=n<<6|(a.dictionary&&32),r[1]|=31-(r[0]<<8|r[1])%31,a.dictionary){var v=Or();v.p(a.dictionary),mr(r,2,v.d())}},Xr=function(r,a){return((r[0]&15)!=8||r[0]>>4>7||(r[0]<<8|r[1])%31)&&Q(6,"invalid zlib data"),(r[1]>>5&1)==1&&Q(6,"invalid zlib data: "+(r[1]&32?"need":"unexpected")+" dictionary"),(r[1]>>3&4)+2};function $r(r,a){a||(a={});var e=Or();e.p(r);var n=Vr(r,a,a.dictionary?6:2,4);return Wr(n,a),mr(n,n.length-4,e.d()),n}function jr(r,a){return Pr(r.subarray(Xr(r),-4),{i:2},a,a)}var Tr=typeof TextEncoder<"u"&&new TextEncoder,kr=typeof TextDecoder<"u"&&new TextDecoder,Yr=0;try{kr.decode(Ir,{stream:!0}),Yr=1}catch{}var Zr=function(r){for(var a="",e=0;;){var n=r[e++],v=(n>127)+(n>223)+(n>239);if(e+v>r.length)return{s:a,r:hr(r,e-1)};v?v==3?(n=((n&15)<<18|(r[e++]&63)<<12|(r[e++]&63)<<6|r[e++]&63)-65536,a+=String.fromCharCode(55296|n>>10,56320|n&1023)):v&1?a+=String.fromCharCode((n&31)<<6|r[e++]&63):a+=String.fromCharCode((n&15)<<12|(r[e++]&63)<<6|r[e++]&63):a+=String.fromCharCode(n)}};function Lr(r,a){if(a){for(var e=new b(r.length),n=0;n<r.length;++n)e[n]=r.charCodeAt(n);return e}if(Tr)return Tr.encode(r);for(var v=r.length,f=new b(r.length+(r.length>>1)),o=0,u=function(l){f[o++]=l},n=0;n<v;++n){if(o+5>f.length){var c=new b(o+8+(v-n<<1));c.set(f),f=c}var t=r.charCodeAt(n);t<128||a?u(t):t<2048?(u(192|t>>6),u(128|t&63)):t>55295&&t<57344?(t=65536+(t&1047552)|r.charCodeAt(++n)&1023,u(240|t>>18),u(128|t>>12&63),u(128|t>>6&63),u(128|t&63)):(u(224|t>>12),u(128|t>>6&63),u(128|t&63))}return hr(f,0,o)}function _r(r,a){if(a){for(var e="",n=0;n<r.length;n+=16384)e+=String.fromCharCode.apply(null,r.subarray(n,n+16384));return e}else{if(kr)return kr.decode(r);var v=Zr(r),f=v.s,e=v.r;return e.length&&Q(8),f}}export{_r as a,Lr as s,jr as u,$r as z};
