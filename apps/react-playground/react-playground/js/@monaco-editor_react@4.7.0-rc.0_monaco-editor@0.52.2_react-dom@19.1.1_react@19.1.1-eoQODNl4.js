import{l as Q}from"./@<EMAIL>";import{r as e,W as C}from"./<EMAIL>";var q={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},B=q,K={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},ee=K;function te({children:t}){return C.createElement("div",{style:ee.container},t)}var re=te,ne=re;function ue({width:t,height:u,isEditorReady:o,loading:r,_ref:g,className:p,wrapperProps:v}){return C.createElement("section",{style:{...B.wrapper,width:t,height:u},...v},!o&&C.createElement(ne,null,r),<PERSON>.createElement("div",{ref:g,style:{...B.fullWidth,...!o&&B.hide},className:p}))}var ie=ue,X=e.memo(ie);function oe(t){e.useEffect(t,[])}var Y=oe;function ae(t,u,o=!0){let r=e.useRef(!0);e.useEffect(r.current||!o?()=>{r.current=!1}:t,u)}var M=ae;function y(){}function V(t,u,o,r){return ce(t,r)||le(t,u,o,r)}function ce(t,u){return t.editor.getModel(Z(t,u))}function le(t,u,o,r){return t.editor.createModel(u,o,r?Z(t,r):void 0)}function Z(t,u){return t.Uri.parse(u)}function se({original:t,modified:u,language:o,originalLanguage:r,modifiedLanguage:g,originalModelPath:p,modifiedModelPath:v,keepCurrentOriginalModel:E=!1,keepCurrentModifiedModel:U=!1,theme:w="light",loading:b="Loading...",options:m={},height:F="100%",width:T="100%",className:W,wrapperProps:_={},beforeMount:I=y,onMount:z=y}){let[h,S]=e.useState(!1),[k,c]=e.useState(!0),l=e.useRef(null),a=e.useRef(null),O=e.useRef(null),d=e.useRef(z),n=e.useRef(I),L=e.useRef(!1);Y(()=>{let i=Q.init();return i.then(s=>(a.current=s)&&c(!1)).catch(s=>s?.type!=="cancelation"&&console.error("Monaco initialization: error:",s)),()=>l.current?x():i.cancel()}),M(()=>{if(l.current&&a.current){let i=l.current.getOriginalEditor(),s=V(a.current,t||"",r||o||"text",p||"");s!==i.getModel()&&i.setModel(s)}},[p],h),M(()=>{if(l.current&&a.current){let i=l.current.getModifiedEditor(),s=V(a.current,u||"",g||o||"text",v||"");s!==i.getModel()&&i.setModel(s)}},[v],h),M(()=>{let i=l.current.getModifiedEditor();i.getOption(a.current.editor.EditorOption.readOnly)?i.setValue(u||""):u!==i.getValue()&&(i.executeEdits("",[{range:i.getModel().getFullModelRange(),text:u||"",forceMoveMarkers:!0}]),i.pushUndoStop())},[u],h),M(()=>{l.current?.getModel()?.original.setValue(t||"")},[t],h),M(()=>{let{original:i,modified:s}=l.current.getModel();a.current.editor.setModelLanguage(i,r||o||"text"),a.current.editor.setModelLanguage(s,g||o||"text")},[o,r,g],h),M(()=>{a.current?.editor.setTheme(w)},[w],h),M(()=>{l.current?.updateOptions(m)},[m],h);let P=e.useCallback(()=>{if(!a.current)return;n.current(a.current);let i=V(a.current,t||"",r||o||"text",p||""),s=V(a.current,u||"",g||o||"text",v||"");l.current?.setModel({original:i,modified:s})},[o,u,g,t,r,p,v]),N=e.useCallback(()=>{!L.current&&O.current&&(l.current=a.current.editor.createDiffEditor(O.current,{automaticLayout:!0,...m}),P(),a.current?.editor.setTheme(w),S(!0),L.current=!0)},[m,w,P]);e.useEffect(()=>{h&&d.current(l.current,a.current)},[h]),e.useEffect(()=>{!k&&!h&&N()},[k,h,N]);function x(){let i=l.current?.getModel();E||i?.original?.dispose(),U||i?.modified?.dispose(),l.current?.dispose()}return C.createElement(X,{width:T,height:F,isEditorReady:h,loading:b,_ref:O,className:W,wrapperProps:_})}var de=se;e.memo(de);function fe(t){let u=e.useRef();return e.useEffect(()=>{u.current=t},[t]),u.current}var ge=fe,D=new Map;function pe({defaultValue:t,defaultLanguage:u,defaultPath:o,value:r,language:g,path:p,theme:v="light",line:E,loading:U="Loading...",options:w={},overrideServices:b={},saveViewState:m=!0,keepCurrentModel:F=!1,width:T="100%",height:W="100%",className:_,wrapperProps:I={},beforeMount:z=y,onMount:h=y,onChange:S,onValidate:k=y}){let[c,l]=e.useState(!1),[a,O]=e.useState(!0),d=e.useRef(null),n=e.useRef(null),L=e.useRef(null),P=e.useRef(h),N=e.useRef(z),x=e.useRef(),i=e.useRef(r),s=ge(p),G=e.useRef(!1),H=e.useRef(!1);Y(()=>{let f=Q.init();return f.then(R=>(d.current=R)&&O(!1)).catch(R=>R?.type!=="cancelation"&&console.error("Monaco initialization: error:",R)),()=>n.current?$():f.cancel()}),M(()=>{let f=V(d.current,t||r||"",u||g||"",p||o||"");f!==n.current?.getModel()&&(m&&D.set(s,n.current?.saveViewState()),n.current?.setModel(f),m&&n.current?.restoreViewState(D.get(p)))},[p],c),M(()=>{n.current?.updateOptions(w)},[w],c),M(()=>{!n.current||r===void 0||(n.current.getOption(d.current.editor.EditorOption.readOnly)?n.current.setValue(r):r!==n.current.getValue()&&(H.current=!0,n.current.executeEdits("",[{range:n.current.getModel().getFullModelRange(),text:r,forceMoveMarkers:!0}]),n.current.pushUndoStop(),H.current=!1))},[r],c),M(()=>{let f=n.current?.getModel();f&&g&&d.current?.editor.setModelLanguage(f,g)},[g],c),M(()=>{E!==void 0&&n.current?.revealLine(E)},[E],c),M(()=>{d.current?.editor.setTheme(v)},[v],c);let J=e.useCallback(()=>{if(!(!L.current||!d.current)&&!G.current){N.current(d.current);let f=p||o,R=V(d.current,r||t||"",u||g||"",f||"");n.current=d.current?.editor.create(L.current,{model:R,automaticLayout:!0,...w},b),m&&n.current.restoreViewState(D.get(f)),d.current.editor.setTheme(v),E!==void 0&&n.current.revealLine(E),l(!0),G.current=!0}},[t,u,o,r,g,p,w,b,m,v,E]);e.useEffect(()=>{c&&P.current(n.current,d.current)},[c]),e.useEffect(()=>{!a&&!c&&J()},[a,c,J]),i.current=r,e.useEffect(()=>{c&&S&&(x.current?.dispose(),x.current=n.current?.onDidChangeModelContent(f=>{H.current||S(n.current.getValue(),f)}))},[c,S]),e.useEffect(()=>{if(c){let f=d.current.editor.onDidChangeMarkers(R=>{let j=n.current.getModel()?.uri;if(j&&R.find(A=>A.path===j.path)){let A=d.current.editor.getModelMarkers({resource:j});k?.(A)}});return()=>{f?.dispose()}}return()=>{}},[c,k]);function $(){x.current?.dispose(),F?m&&D.set(p,n.current.saveViewState()):n.current.getModel()?.dispose(),n.current.dispose()}return C.createElement(X,{width:T,height:W,isEditorReady:c,loading:U,_ref:L,className:_,wrapperProps:I})}var he=pe,Me=e.memo(he),we=Me;export{we as F};
