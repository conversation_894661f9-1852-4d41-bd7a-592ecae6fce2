$file-name-list-height: 38px;

.CodeEditor {
  height: 100%;
  :global {
    .file-name-list {
      height: $file-name-list-height;
    }
    .editor {
      height: calc(100% - #{$file-name-list-height});
    }
  }
}

.fileNameList {
  display: flex;
  align-items: center;

  height: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  border-bottom: 1px solid #ddd;
  box-sizing: border-box;

  color: var(--react-playground-text);
  background-color: var(--react-playground-bg);

  &::-webkit-scrollbar {
    height: 1px;
  }

  &::-webkit-scrollbar-track {
    background-color: #ddd;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #ddd;
  }
}

.tabItem {
  display: inline-flex;
  padding: 8px 10px 6px;
  font-size: 13px;
  line-height: 20px;
  cursor: pointer;
  align-items: center;
  border-bottom: 1px solid transparent;

  &.selectTabItem {
    color: #62d5fa;
    border-bottom: 3px solid #62d5fa;
  }
}

.tabsItemInput {
  width: 90px;
  padding: 4px 0 4px 10px;
  font-size: 13px;

  color: #444;
  background-color: #ddd;
  border: 1px solid #ddd;
  border-radius: 4px;
  outline: none;
}

.add {
  cursor: pointer;
  font-size: 22px;
}