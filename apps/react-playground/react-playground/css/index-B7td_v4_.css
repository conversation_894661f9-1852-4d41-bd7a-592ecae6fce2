*{margin:0;padding:0}._header_16kaf_1{display:flex;align-items:center;justify-content:space-between;height:60px;border-bottom:1px solid #ddd;color:var(--react-playground-text);background-color:var(--react-playground-bg);padding:0 20px}._logo_16kaf_13{display:flex;align-items:center;flex:1}._logo_16kaf_13 .title{margin-left:10px;font-weight:500}._link_16kaf_23{display:flex;justify-content:flex-end;flex:1}._link_16kaf_23 .icon{width:24px;height:24px;margin-left:10px;cursor:pointer}._CodeEditor_1uyzt_1{height:100%}._CodeEditor_1uyzt_1 .file-name-list{height:38px}._CodeEditor_1uyzt_1 .editor{height:calc(100% - 38px)}._fileNameList_1uyzt_11{display:flex;align-items:center;height:100%;overflow-x:auto;overflow-y:hidden;border-bottom:1px solid #ddd;box-sizing:border-box;color:var(--react-playground-text);background-color:var(--react-playground-bg)}._fileNameList_1uyzt_11::-webkit-scrollbar{height:1px}._fileNameList_1uyzt_11::-webkit-scrollbar-track{background-color:#ddd}._fileNameList_1uyzt_11::-webkit-scrollbar-thumb{background-color:#ddd}._tabItem_1uyzt_32{display:inline-flex;padding:8px 10px 6px;font-size:13px;line-height:20px;cursor:pointer;align-items:center;border-bottom:1px solid transparent}._tabItem_1uyzt_32._selectTabItem_1uyzt_41{color:#62d5fa;border-bottom:3px solid #62d5fa}._tabsItemInput_1uyzt_46{width:90px;padding:4px 0 4px 10px;font-size:13px;color:#444;background-color:#ddd;border:1px solid #ddd;border-radius:4px;outline:none}._add_1uyzt_57{cursor:pointer;font-size:22px}._msg_v5z09_1{position:absolute;right:8px;bottom:0;left:8px;z-index:10;display:flex;max-height:calc(100% - 300px);min-height:40px;margin-bottom:8px;color:var(--color);background-color:var(--bg-color);border:2px solid #fff;border-radius:6px;border-color:var(--color)}._msg_v5z09_1._error_v5z09_17{--color: #f56c6c;--bg-color: #fef0f0}._msg_v5z09_1._warn_v5z09_21{--color: #e6a23c;--bg-color: #fdf6ec}pre{padding:12px 20px;margin:0;overflow:auto;white-space:break-spaces}._dismiss_v5z09_33{position:absolute;top:2px;right:2px;display:block;width:18px;height:18px;padding:0;font-size:9px;line-height:18px;color:var(--bg-color);text-align:center;cursor:pointer;background-color:var(--color);border:none;border-radius:9px}.react_playground_light{--react-playground-text: #444;--react-playground-bg: #fff}.react_playground_dark{--react-playground-text: #fff;--react-playground-bg: #1a1a1a}
