{"name": "qk-main-vue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build --mode production", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "element-plus": "^2.10.7", "qiankun": "^2.10.16", "vue": "^3.5.18"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "unplugin-element-plus": "^0.10.0", "vite": "^7.1.2", "vue-tsc": "^3.0.5"}}