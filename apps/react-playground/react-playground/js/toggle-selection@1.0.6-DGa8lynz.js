var r,o;function c(){return o||(o=1,r=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var n=document.activeElement,a=[],t=0;t<e.rangeCount;t++)a.push(e.getRangeAt(t));switch(n.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":n.blur();break;default:n=null;break}return e.removeAllRanges(),function(){e.type==="Caret"&&e.removeAllRanges(),e.rangeCount||a.forEach(function(u){e.addRange(u)}),n&&n.focus()}}),r}export{c as r};
