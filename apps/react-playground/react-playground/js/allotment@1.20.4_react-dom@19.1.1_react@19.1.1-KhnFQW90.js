import{r as L,W as ve}from"./<EMAIL>";function He(){return He=Object.assign?Object.assign.bind():function(u){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var r in i)({}).hasOwnProperty.call(i,r)&&(u[r]=i[r])}return u},He.apply(null,arguments)}var ze=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ye(u){return u&&u.__esModule&&Object.prototype.hasOwnProperty.call(u,"default")?u.default:u}var Ht,Yt={exports:{}};/*!
  Copyright (c) 2018 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/var pt,Bt,Wt,Oi=(Ht||(Ht=1,pt=Yt,(function(){var u={}.hasOwnProperty;function e(){for(var i=[],r=0;r<arguments.length;r++){var l=arguments[r];if(l){var d=typeof l;if(d==="string"||d==="number")i.push(l);else if(Array.isArray(l)){if(l.length){var _=e.apply(null,l);_&&i.push(_)}}else if(d==="object")if(l.toString===Object.prototype.toString)for(var m in l)u.call(l,m)&&l[m]&&i.push(m);else i.push(l.toString())}}return i.join(" ")}pt.exports?(e.default=e,pt.exports=e):window.classNames=e})()),Yt.exports),zt=Ye(Oi),Gt,oe=Ye((function(){if(Wt)return Bt;Wt=1;var u=/^\s+|\s+$/g,e=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,r=/^0o[0-7]+$/i,l=parseInt,d=Object.prototype.toString;function _(n){var o=typeof n;return!!n&&(o=="object"||o=="function")}function m(n){if(typeof n=="number")return n;if((function(a){return typeof a=="symbol"||(function(g){return!!g&&typeof g=="object"})(a)&&d.call(a)=="[object Symbol]"})(n))return NaN;if(_(n)){var o=typeof n.valueOf=="function"?n.valueOf():n;n=_(o)?o+"":o}if(typeof n!="string")return n===0?n:+n;n=n.replace(u,"");var c=i.test(n);return c||r.test(n)?l(n.slice(2),c?2:8):e.test(n)?NaN:+n}return Bt=function(n,o,c){return c===void 0&&(c=o,o=void 0),c!==void 0&&(c=(c=m(c))==c?c:0),o!==void 0&&(o=(o=m(o))==o?o:0),(function(a,g,y){return a==a&&(y!==void 0&&(a=a<=y?a:y),g!==void 0&&(a=a>=g?a:g)),a})(m(n),o,c)}})()),yt={exports:{}},Ni=(Gt||(Gt=1,(function(u,e){var i="__lodash_hash_undefined__",r=9007199254740991,l="[object Arguments]",d="[object Array]",_="[object Boolean]",m="[object Date]",n="[object Error]",o="[object Function]",c="[object Map]",a="[object Number]",g="[object Object]",y="[object Promise]",v="[object RegExp]",z="[object Set]",S="[object String]",p="[object Symbol]",w="[object WeakMap]",I="[object ArrayBuffer]",E="[object DataView]",F=/^\[object .+?Constructor\]$/,J=/^(?:0|[1-9]\d*)$/,h={};h["[object Float32Array]"]=h["[object Float64Array]"]=h["[object Int8Array]"]=h["[object Int16Array]"]=h["[object Int32Array]"]=h["[object Uint8Array]"]=h["[object Uint8ClampedArray]"]=h["[object Uint16Array]"]=h["[object Uint32Array]"]=!0,h[l]=h[d]=h[I]=h[_]=h[E]=h[m]=h[n]=h[o]=h[c]=h[a]=h[g]=h[v]=h[z]=h[S]=h[w]=!1;var O=typeof ze=="object"&&ze&&ze.Object===Object&&ze,Z=typeof self=="object"&&self&&self.Object===Object&&self,R=O||Z||Function("return this")(),ae=e&&!e.nodeType&&e,N=ae&&u&&!u.nodeType&&u,D=N&&N.exports===ae,$=D&&O.process,M=(function(){try{return $&&$.binding&&$.binding("util")}catch{}})(),P=M&&M.isTypedArray;function T(t,s){for(var f=-1,b=t==null?0:t.length;++f<b;)if(s(t[f],f,t))return!0;return!1}function G(t){var s=-1,f=Array(t.size);return t.forEach(function(b,k){f[++s]=[k,b]}),f}function Se(t){var s=-1,f=Array(t.size);return t.forEach(function(b){f[++s]=b}),f}var pe,K,ye,q=Array.prototype,le=Function.prototype,ce=Object.prototype,De=R["__core-js_shared__"],Le=le.toString,x=ce.hasOwnProperty,V=(pe=/[^.]+$/.exec(De&&De.keys&&De.keys.IE_PROTO||""))?"Symbol(src)_1."+pe:"",H=ce.toString,B=RegExp("^"+Le.call(x).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),xt=D?R.Buffer:void 0,Be=R.Symbol,Et=R.Uint8Array,Vt=ce.propertyIsEnumerable,di=q.splice,Ie=Be?Be.toStringTag:void 0,jt=Object.getOwnPropertySymbols,vi=xt?xt.isBuffer:void 0,pi=(K=Object.keys,ye=Object,function(t){return K(ye(t))}),at=Oe(R,"DataView"),Ae=Oe(R,"Map"),ut=Oe(R,"Promise"),lt=Oe(R,"Set"),ct=Oe(R,"WeakMap"),Ce=Oe(Object,"create"),yi=Ve(at),gi=Ve(Ae),_i=Ve(ut),bi=Ve(lt),wi=Ve(ct),Dt=Be?Be.prototype:void 0,ht=Dt?Dt.valueOf:void 0;function xe(t){var s=-1,f=t==null?0:t.length;for(this.clear();++s<f;){var b=t[s];this.set(b[0],b[1])}}function he(t){var s=-1,f=t==null?0:t.length;for(this.clear();++s<f;){var b=t[s];this.set(b[0],b[1])}}function Ee(t){var s=-1,f=t==null?0:t.length;for(this.clear();++s<f;){var b=t[s];this.set(b[0],b[1])}}function We(t){var s=-1,f=t==null?0:t.length;for(this.__data__=new Ee;++s<f;)this.add(t[s])}function ge(t){var s=this.__data__=new he(t);this.size=s.size}function zi(t,s){var f=Xe(t),b=!f&&Vi(t),k=!f&&!b&&ft(t),j=!f&&!b&&!k&&Ft(t),Y=f||b||k||j,W=Y?(function(X,ue){for(var fe=-1,Q=Array(X);++fe<X;)Q[fe]=ue(fe);return Q})(t.length,String):[],se=W.length;for(var U in t)!x.call(t,U)||Y&&(U=="length"||k&&(U=="offset"||U=="parent")||j&&(U=="buffer"||U=="byteLength"||U=="byteOffset")||Ei(U,se))||W.push(U);return W}function Ge(t,s){for(var f=t.length;f--;)if(Pt(t[f][0],s))return f;return-1}function Te(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":Ie&&Ie in Object(t)?(function(s){var f=x.call(s,Ie),b=s[Ie];try{s[Ie]=void 0;var k=!0}catch{}var j=H.call(s);return k&&(f?s[Ie]=b:delete s[Ie]),j})(t):(function(s){return H.call(s)})(t)}function Lt(t){return Fe(t)&&Te(t)==l}function Ot(t,s,f,b,k){return t===s||(t==null||s==null||!Fe(t)&&!Fe(s)?t!=t&&s!=s:(function(j,Y,W,se,U,X){var ue=Xe(j),fe=Xe(Y),Q=ue?d:_e(j),me=fe?d:_e(Y),Ne=(Q=Q==l?g:Q)==g,Je=(me=me==l?g:me)==g,Me=Q==me;if(Me&&ft(j)){if(!ft(Y))return!1;ue=!0,Ne=!1}if(Me&&!Ne)return X||(X=new ge),ue||Ft(j)?Nt(j,Y,W,se,U,X):(function(C,A,Ke,be,mt,te,de){switch(Ke){case E:if(C.byteLength!=A.byteLength||C.byteOffset!=A.byteOffset)return!1;C=C.buffer,A=A.buffer;case I:return!(C.byteLength!=A.byteLength||!te(new Et(C),new Et(A)));case _:case m:case a:return Pt(+C,+A);case n:return C.name==A.name&&C.message==A.message;case v:case S:return C==A+"";case c:var we=G;case z:var Re=1&be;if(we||(we=Se),C.size!=A.size&&!Re)return!1;var Ze=de.get(C);if(Ze)return Ze==A;be|=2,de.set(C,A);var dt=Nt(we(C),we(A),be,mt,te,de);return de.delete(C),dt;case p:if(ht)return ht.call(C)==ht.call(A)}return!1})(j,Y,Q,W,se,U,X);if(!(1&W)){var ke=Ne&&x.call(j,"__wrapped__"),kt=Je&&x.call(Y,"__wrapped__");if(ke||kt){var Di=ke?j.value():j,Li=kt?Y.value():Y;return X||(X=new ge),U(Di,Li,W,se,X)}}return!!Me&&(X||(X=new ge),(function(C,A,Ke,be,mt,te){var de=1&Ke,we=Mt(C),Re=we.length,Ze=Mt(A),dt=Ze.length;if(Re!=dt&&!de)return!1;for(var Qe=Re;Qe--;){var je=we[Qe];if(!(de?je in A:x.call(A,je)))return!1}var Rt=te.get(C);if(Rt&&te.get(A))return Rt==A;var qe=!0;te.set(C,A),te.set(A,C);for(var vt=de;++Qe<Re;){var et=C[je=we[Qe]],tt=A[je];if(be)var $t=de?be(tt,et,je,A,C,te):be(et,tt,je,C,A,te);if(!($t===void 0?et===tt||mt(et,tt,Ke,be,te):$t)){qe=!1;break}vt||(vt=je=="constructor")}if(qe&&!vt){var it=C.constructor,nt=A.constructor;it==nt||!("constructor"in C)||!("constructor"in A)||typeof it=="function"&&it instanceof it&&typeof nt=="function"&&nt instanceof nt||(qe=!1)}return te.delete(C),te.delete(A),qe})(j,Y,W,se,U,X))})(t,s,f,b,Ot,k))}function Si(t){return!(!Tt(t)||(function(s){return!!V&&V in s})(t))&&(At(t)?B:F).test(Ve(t))}function Ii(t){if(f=(s=t)&&s.constructor,b=typeof f=="function"&&f.prototype||ce,s!==b)return pi(t);var s,f,b,k=[];for(var j in Object(t))x.call(t,j)&&j!="constructor"&&k.push(j);return k}function Nt(t,s,f,b,k,j){var Y=1&f,W=t.length,se=s.length;if(W!=se&&!(Y&&se>W))return!1;var U=j.get(t);if(U&&j.get(s))return U==s;var X=-1,ue=!0,fe=2&f?new We:void 0;for(j.set(t,s),j.set(s,t);++X<W;){var Q=t[X],me=s[X];if(b)var Ne=Y?b(me,Q,X,s,t,j):b(Q,me,X,t,s,j);if(Ne!==void 0){if(Ne)continue;ue=!1;break}if(fe){if(!T(s,function(Je,Me){if(ke=Me,!fe.has(ke)&&(Q===Je||k(Q,Je,f,b,j)))return fe.push(Me);var ke})){ue=!1;break}}else if(Q!==me&&!k(Q,me,f,b,j)){ue=!1;break}}return j.delete(t),j.delete(s),ue}function Mt(t){return(function(s,f,b){var k=f(s);return Xe(s)?k:(function(j,Y){for(var W=-1,se=Y.length,U=j.length;++W<se;)j[U+W]=Y[W];return j})(k,b(s))})(t,ji,xi)}function Ue(t,s){var f,b,k=t.__data__;return((b=typeof(f=s))=="string"||b=="number"||b=="symbol"||b=="boolean"?f!=="__proto__":f===null)?k[typeof s=="string"?"string":"hash"]:k.map}function Oe(t,s){var f=(function(b,k){return b?.[k]})(t,s);return Si(f)?f:void 0}xe.prototype.clear=function(){this.__data__=Ce?Ce(null):{},this.size=0},xe.prototype.delete=function(t){var s=this.has(t)&&delete this.__data__[t];return this.size-=s?1:0,s},xe.prototype.get=function(t){var s=this.__data__;if(Ce){var f=s[t];return f===i?void 0:f}return x.call(s,t)?s[t]:void 0},xe.prototype.has=function(t){var s=this.__data__;return Ce?s[t]!==void 0:x.call(s,t)},xe.prototype.set=function(t,s){var f=this.__data__;return this.size+=this.has(t)?0:1,f[t]=Ce&&s===void 0?i:s,this},he.prototype.clear=function(){this.__data__=[],this.size=0},he.prototype.delete=function(t){var s=this.__data__,f=Ge(s,t);return!(f<0||(f==s.length-1?s.pop():di.call(s,f,1),--this.size,0))},he.prototype.get=function(t){var s=this.__data__,f=Ge(s,t);return f<0?void 0:s[f][1]},he.prototype.has=function(t){return Ge(this.__data__,t)>-1},he.prototype.set=function(t,s){var f=this.__data__,b=Ge(f,t);return b<0?(++this.size,f.push([t,s])):f[b][1]=s,this},Ee.prototype.clear=function(){this.size=0,this.__data__={hash:new xe,map:new(Ae||he),string:new xe}},Ee.prototype.delete=function(t){var s=Ue(this,t).delete(t);return this.size-=s?1:0,s},Ee.prototype.get=function(t){return Ue(this,t).get(t)},Ee.prototype.has=function(t){return Ue(this,t).has(t)},Ee.prototype.set=function(t,s){var f=Ue(this,t),b=f.size;return f.set(t,s),this.size+=f.size==b?0:1,this},We.prototype.add=We.prototype.push=function(t){return this.__data__.set(t,i),this},We.prototype.has=function(t){return this.__data__.has(t)},ge.prototype.clear=function(){this.__data__=new he,this.size=0},ge.prototype.delete=function(t){var s=this.__data__,f=s.delete(t);return this.size=s.size,f},ge.prototype.get=function(t){return this.__data__.get(t)},ge.prototype.has=function(t){return this.__data__.has(t)},ge.prototype.set=function(t,s){var f=this.__data__;if(f instanceof he){var b=f.__data__;if(!Ae||b.length<199)return b.push([t,s]),this.size=++f.size,this;f=this.__data__=new Ee(b)}return f.set(t,s),this.size=f.size,this};var xi=jt?function(t){return t==null?[]:(t=Object(t),(function(s,f){for(var b=-1,k=s==null?0:s.length,j=0,Y=[];++b<k;){var W=s[b];f(W,b,s)&&(Y[j++]=W)}return Y})(jt(t),function(s){return Vt.call(t,s)}))}:function(){return[]},_e=Te;function Ei(t,s){return!!(s=s??r)&&(typeof t=="number"||J.test(t))&&t>-1&&t%1==0&&t<s}function Ve(t){if(t!=null){try{return Le.call(t)}catch{}try{return t+""}catch{}}return""}function Pt(t,s){return t===s||t!=t&&s!=s}(at&&_e(new at(new ArrayBuffer(1)))!=E||Ae&&_e(new Ae)!=c||ut&&_e(ut.resolve())!=y||lt&&_e(new lt)!=z||ct&&_e(new ct)!=w)&&(_e=function(t){var s=Te(t),f=s==g?t.constructor:void 0,b=f?Ve(f):"";if(b)switch(b){case yi:return E;case gi:return c;case _i:return y;case bi:return z;case wi:return w}return s});var Vi=Lt((function(){return arguments})())?Lt:function(t){return Fe(t)&&x.call(t,"callee")&&!Vt.call(t,"callee")},Xe=Array.isArray,ft=vi||function(){return!1};function At(t){if(!Tt(t))return!1;var s=Te(t);return s==o||s=="[object GeneratorFunction]"||s=="[object AsyncFunction]"||s=="[object Proxy]"}function Ct(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=r}function Tt(t){var s=typeof t;return t!=null&&(s=="object"||s=="function")}function Fe(t){return t!=null&&typeof t=="object"}var Ft=P?(function(t){return function(s){return t(s)}})(P):function(t){return Fe(t)&&Ct(t.length)&&!!h[Te(t)]};function ji(t){return(s=t)!=null&&Ct(s.length)&&!At(s)?zi(t):Ii(t);var s}u.exports=function(t,s){return Ot(t,s)}})(yt,yt.exports)),yt.exports),Mi=Ye(Ni);function Ut(u,e,i){return u[e]?u[e][0]?u[e][0][i]:u[e][i]:e==="contentBoxSize"?u.contentRect[i==="inlineSize"?"width":"height"]:void 0}function Pi(u){u===void 0&&(u={});var e=u.onResize,i=L.useRef(void 0);i.current=e;var r=u.round||Math.round,l=L.useRef(),d=L.useState({width:void 0,height:void 0}),_=d[0],m=d[1],n=L.useRef(!1);L.useEffect(function(){return n.current=!1,function(){n.current=!0}},[]);var o=L.useRef({width:void 0,height:void 0}),c=(function(a,g){var y=L.useRef(null),v=L.useRef(null);v.current=g;var z=L.useRef(null);L.useEffect(function(){S()});var S=L.useCallback(function(){var p=z.current,w=v.current,I=p||(w?w instanceof Element?w:w.current:null);y.current&&y.current.element===I&&y.current.subscriber===a||(y.current&&y.current.cleanup&&y.current.cleanup(),y.current={element:I,subscriber:a,cleanup:I?a(I):void 0})},[a]);return L.useEffect(function(){return function(){y.current&&y.current.cleanup&&(y.current.cleanup(),y.current=null)}},[]),L.useCallback(function(p){z.current=p,S()},[S])})(L.useCallback(function(a){return l.current&&l.current.box===u.box&&l.current.round===r||(l.current={box:u.box,round:r,instance:new ResizeObserver(function(g){var y=g[0],v=u.box==="border-box"?"borderBoxSize":u.box==="device-pixel-content-box"?"devicePixelContentBoxSize":"contentBoxSize",z=Ut(y,v,"inlineSize"),S=Ut(y,v,"blockSize"),p=z?r(z):void 0,w=S?r(S):void 0;if(o.current.width!==p||o.current.height!==w){var I={width:p,height:w};o.current.width=p,o.current.height=w,i.current?i.current(I):n.current||m(I)}})}),l.current.instance.observe(a,{box:u.box}),function(){l.current&&l.current.instance.unobserve(a)}},[u.box,r]),u.ref);return L.useMemo(function(){return{ref:c,width:_.width,height:_.height}},[c,_.width,_.height])}var Ai="allotment-module_splitView__L-yRc",Ci="allotment-module_sashContainer__fzwJF",Ti="allotment-module_splitViewContainer__rQnVa",oi="allotment-module_splitViewView__MGZ6O",Fi="allotment-module_vertical__WSwwa",ki="allotment-module_horizontal__7doS8",Ri="allotment-module_separatorBorder__x-rDS";let $e,ai=!1,ui=!1;typeof navigator=="object"&&($e=navigator.userAgent,ui=$e.indexOf("Macintosh")>=0,ai=($e.indexOf("Macintosh")>=0||$e.indexOf("iPad")>=0||$e.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0);const li=ai,$i=ui,Xt=typeof window<"u"&&window.document!==void 0&&window.document.createElement!==void 0?L.useLayoutEffect:L.useEffect;class Hi{constructor(){this._size=void 0}getSize(){return this._size}setSize(e){this._size=e}}function rt(u,e){const i=u.length,r=i-e.length;return r>=0&&u.slice(r,i)===e}var Jt,Kt={exports:{}},Zt,Qt,Yi=(Jt||(Jt=1,(function(u){var e=Object.prototype.hasOwnProperty,i="~";function r(){}function l(n,o,c){this.fn=n,this.context=o,this.once=c||!1}function d(n,o,c,a,g){if(typeof c!="function")throw new TypeError("The listener must be a function");var y=new l(c,a||n,g),v=i?i+o:o;return n._events[v]?n._events[v].fn?n._events[v]=[n._events[v],y]:n._events[v].push(y):(n._events[v]=y,n._eventsCount++),n}function _(n,o){--n._eventsCount==0?n._events=new r:delete n._events[o]}function m(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),new r().__proto__||(i=!1)),m.prototype.eventNames=function(){var n,o,c=[];if(this._eventsCount===0)return c;for(o in n=this._events)e.call(n,o)&&c.push(i?o.slice(1):o);return Object.getOwnPropertySymbols?c.concat(Object.getOwnPropertySymbols(n)):c},m.prototype.listeners=function(n){var o=i?i+n:n,c=this._events[o];if(!c)return[];if(c.fn)return[c.fn];for(var a=0,g=c.length,y=new Array(g);a<g;a++)y[a]=c[a].fn;return y},m.prototype.listenerCount=function(n){var o=i?i+n:n,c=this._events[o];return c?c.fn?1:c.length:0},m.prototype.emit=function(n,o,c,a,g,y){var v=i?i+n:n;if(!this._events[v])return!1;var z,S,p=this._events[v],w=arguments.length;if(p.fn){switch(p.once&&this.removeListener(n,p.fn,void 0,!0),w){case 1:return p.fn.call(p.context),!0;case 2:return p.fn.call(p.context,o),!0;case 3:return p.fn.call(p.context,o,c),!0;case 4:return p.fn.call(p.context,o,c,a),!0;case 5:return p.fn.call(p.context,o,c,a,g),!0;case 6:return p.fn.call(p.context,o,c,a,g,y),!0}for(S=1,z=new Array(w-1);S<w;S++)z[S-1]=arguments[S];p.fn.apply(p.context,z)}else{var I,E=p.length;for(S=0;S<E;S++)switch(p[S].once&&this.removeListener(n,p[S].fn,void 0,!0),w){case 1:p[S].fn.call(p[S].context);break;case 2:p[S].fn.call(p[S].context,o);break;case 3:p[S].fn.call(p[S].context,o,c);break;case 4:p[S].fn.call(p[S].context,o,c,a);break;default:if(!z)for(I=1,z=new Array(w-1);I<w;I++)z[I-1]=arguments[I];p[S].fn.apply(p[S].context,z)}}return!0},m.prototype.on=function(n,o,c){return d(this,n,o,c,!1)},m.prototype.once=function(n,o,c){return d(this,n,o,c,!0)},m.prototype.removeListener=function(n,o,c,a){var g=i?i+n:n;if(!this._events[g])return this;if(!o)return _(this,g),this;var y=this._events[g];if(y.fn)y.fn!==o||a&&!y.once||c&&y.context!==c||_(this,g);else{for(var v=0,z=[],S=y.length;v<S;v++)(y[v].fn!==o||a&&!y[v].once||c&&y[v].context!==c)&&z.push(y[v]);z.length?this._events[g]=z.length===1?z[0]:z:_(this,g)}return this},m.prototype.removeAllListeners=function(n){var o;return n?(o=i?i+n:n,this._events[o]&&_(this,o)):(this._events=new r,this._eventsCount=0),this},m.prototype.off=m.prototype.removeListener,m.prototype.addListener=m.prototype.on,m.prefixed=i,m.EventEmitter=m,u.exports=m})(Kt)),Kt.exports),St=Ye(Yi);function qt(u,e){const i=u.indexOf(e);i>-1&&(u.splice(i,1),u.unshift(e))}function gt(u,e){const i=u.indexOf(e);i>-1&&(u.splice(i,1),u.push(e))}function ie(u,e,i=1){const r=Math.max(0,Math.ceil((e-u)/i)),l=new Array(r);let d=-1;for(;++d<r;)l[d]=u+d*i;return l}var Bi=Ye((function(){if(Qt)return Zt;Qt=1;var u=/^\s+|\s+$/g,e=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,r=/^0o[0-7]+$/i,l=parseInt,d=typeof ze=="object"&&ze&&ze.Object===Object&&ze,_=typeof self=="object"&&self&&self.Object===Object&&self,m=d||_||Function("return this")(),n=Object.prototype.toString,o=Math.max,c=Math.min,a=function(){return m.Date.now()};function g(v){var z=typeof v;return!!v&&(z=="object"||z=="function")}function y(v){if(typeof v=="number")return v;if((function(p){return typeof p=="symbol"||(function(w){return!!w&&typeof w=="object"})(p)&&n.call(p)=="[object Symbol]"})(v))return NaN;if(g(v)){var z=typeof v.valueOf=="function"?v.valueOf():v;v=g(z)?z+"":z}if(typeof v!="string")return v===0?v:+v;v=v.replace(u,"");var S=i.test(v);return S||r.test(v)?l(v.slice(2),S?2:8):e.test(v)?NaN:+v}return Zt=function(v,z,S){var p,w,I,E,F,J,h=0,O=!1,Z=!1,R=!0;if(typeof v!="function")throw new TypeError("Expected a function");function ae(P){var T=p,G=w;return p=w=void 0,h=P,E=v.apply(G,T)}function N(P){var T=P-J;return J===void 0||T>=z||T<0||Z&&P-h>=I}function D(){var P=a();if(N(P))return $(P);F=setTimeout(D,(function(T){var G=z-(T-J);return Z?c(G,I-(T-h)):G})(P))}function $(P){return F=void 0,R&&p?ae(P):(p=w=void 0,E)}function M(){var P=a(),T=N(P);if(p=arguments,w=this,J=P,T){if(F===void 0)return(function(G){return h=G,F=setTimeout(D,z),O?ae(G):E})(J);if(Z)return F=setTimeout(D,z),ae(J)}return F===void 0&&(F=setTimeout(D,z)),E}return z=y(z)||0,g(S)&&(O=!!S.leading,I=(Z="maxWait"in S)?o(y(S.maxWait)||0,z):I,R="trailing"in S?!!S.trailing:R),M.cancel=function(){F!==void 0&&clearTimeout(F),h=0,p=J=w=F=void 0},M.flush=function(){return F===void 0?E:$(a())},M}})()),Wi="sash-module_sash__K-9lB",Gi="sash-module_disabled__Hm-wx",Ui="sash-module_mac__Jf6OJ",ei="sash-module_vertical__pB-rs",Xi="sash-module_minimum__-UKxp",Ji="sash-module_maximum__TCWxD",ti="sash-module_horizontal__kFbiw",_t="sash-module_hover__80W6I",bt="sash-module_active__bJspD";let re=(function(u){return u.Vertical="VERTICAL",u.Horizontal="HORIZONTAL",u})({}),ee=(function(u){return u.Disabled="DISABLED",u.Minimum="MINIMUM",u.Maximum="MAXIMUM",u.Enabled="ENABLED",u})({}),ci=li?20:8;const hi=new St;class ii extends St{get state(){return this._state}set state(e){this._state!==e&&(this.el.classList.toggle(Gi,e===ee.Disabled),this.el.classList.toggle("sash-disabled",e===ee.Disabled),this.el.classList.toggle(Xi,e===ee.Minimum),this.el.classList.toggle("sash-minimum",e===ee.Minimum),this.el.classList.toggle(Ji,e===ee.Maximum),this.el.classList.toggle("sash-maximum",e===ee.Maximum),this._state=e,this.emit("enablementChange",e))}constructor(e,i,r){var l;super(),this.el=void 0,this.layoutProvider=void 0,this.orientation=void 0,this.size=void 0,this.hoverDelay=300,this.hoverDelayer=Bi(d=>d.classList.add("sash-hover",_t),this.hoverDelay),this._state=ee.Enabled,this.onPointerStart=d=>{const _=d.pageX,m=d.pageY,n={startX:_,currentX:_,startY:m,currentY:m};this.el.classList.add("sash-active",bt),this.emit("start",n),this.el.setPointerCapture(d.pointerId);const o=a=>{a.preventDefault();const g={startX:_,currentX:a.pageX,startY:m,currentY:a.pageY};this.emit("change",g)},c=a=>{a.preventDefault(),this.el.classList.remove("sash-active",bt),this.hoverDelayer.cancel(),this.emit("end"),this.el.releasePointerCapture(a.pointerId),window.removeEventListener("pointermove",o),window.removeEventListener("pointerup",c)};window.addEventListener("pointermove",o),window.addEventListener("pointerup",c)},this.onPointerDoublePress=()=>{this.emit("reset")},this.onMouseEnter=()=>{this.el.classList.contains(bt)?(this.hoverDelayer.cancel(),this.el.classList.add("sash-hover",_t)):this.hoverDelayer(this.el)},this.onMouseLeave=()=>{this.hoverDelayer.cancel(),this.el.classList.remove("sash-hover",_t)},this.el=document.createElement("div"),this.el.classList.add("sash",Wi),this.el.dataset.testid="sash",e.append(this.el),$i&&this.el.classList.add("sash-mac",Ui),this.el.addEventListener("pointerdown",this.onPointerStart),this.el.addEventListener("dblclick",this.onPointerDoublePress),this.el.addEventListener("mouseenter",this.onMouseEnter),this.el.addEventListener("mouseleave",this.onMouseLeave),typeof r.size=="number"?(this.size=r.size,r.orientation===re.Vertical?this.el.style.width=`${this.size}px`:this.el.style.height=`${this.size}px`):(this.size=ci,hi.on("onDidChangeGlobalSize",d=>{this.size=d,this.layout()})),this.layoutProvider=i,this.orientation=(l=r.orientation)!=null?l:re.Vertical,this.orientation===re.Horizontal?(this.el.classList.add("sash-horizontal",ti),this.el.classList.remove("sash-vertical",ei)):(this.el.classList.remove("sash-horizontal",ti),this.el.classList.add("sash-vertical",ei)),this.layout()}layout(){if(this.orientation===re.Vertical){const e=this.layoutProvider;this.el.style.left=e.getVerticalSashLeft(this)-this.size/2+"px",e.getVerticalSashTop&&(this.el.style.top=e.getVerticalSashTop(this)+"px"),e.getVerticalSashHeight&&(this.el.style.height=e.getVerticalSashHeight(this)+"px")}else{const e=this.layoutProvider;this.el.style.top=e.getHorizontalSashTop(this)-this.size/2+"px",e.getHorizontalSashLeft&&(this.el.style.left=e.getHorizontalSashLeft(this)+"px"),e.getHorizontalSashWidth&&(this.el.style.width=e.getHorizontalSashWidth(this)+"px")}}dispose(){this.el.removeEventListener("pointerdown",this.onPointerStart),this.el.removeEventListener("dblclick",this.onPointerDoublePress),this.el.removeEventListener("mouseenter",this.onMouseEnter),this.el.removeEventListener("mouseleave",()=>this.onMouseLeave),this.el.remove()}}let ot;var wt;(wt=ot||(ot={})).Distribute={type:"distribute"},wt.Split=function(u){return{type:"split",index:u}},wt.Invisible=function(u){return{type:"invisible",cachedVisibleSize:u}};let ne=(function(u){return u.Normal="NORMAL",u.Low="LOW",u.High="HIGH",u})({});class fi{constructor(e,i,r){this.container=void 0,this.view=void 0,this._size=void 0,this._cachedVisibleSize=void 0,this.container=e,this.view=i,this.container.classList.add("split-view-view",oi),this.container.dataset.testid="split-view-view",typeof r=="number"?(this._size=r,this._cachedVisibleSize=void 0,e.classList.add("split-view-view-visible")):(this._size=0,this._cachedVisibleSize=r.cachedVisibleSize)}set size(e){this._size=e}get size(){return this._size}get priority(){return this.view.priority}get snap(){return!!this.view.snap}get cachedVisibleSize(){return this._cachedVisibleSize}get visible(){return this._cachedVisibleSize===void 0}setVisible(e,i){e!==this.visible&&(e?(this.size=oe(this._cachedVisibleSize,this.viewMinimumSize,this.viewMaximumSize),this._cachedVisibleSize=void 0):(this._cachedVisibleSize=typeof i=="number"?i:this.size,this.size=0),this.container.classList.toggle("split-view-view-visible",e),this.view.setVisible&&this.view.setVisible(e))}get minimumSize(){return this.visible?this.view.minimumSize:0}get viewMinimumSize(){return this.view.minimumSize}get maximumSize(){return this.visible?this.view.maximumSize:0}get viewMaximumSize(){return this.view.maximumSize}set enabled(e){this.container.style.pointerEvents=e?"":"none"}layout(e){this.layoutContainer(e),this.view.layout(this.size,e)}}class Ki extends fi{layoutContainer(e){this.container.style.left=`${e}px`,this.container.style.width=`${this.size}px`}}class Zi extends fi{layoutContainer(e){this.container.style.top=`${e}px`,this.container.style.height=`${this.size}px`}}class Qi extends St{get startSnappingEnabled(){return this._startSnappingEnabled}set startSnappingEnabled(e){this._startSnappingEnabled!==e&&(this._startSnappingEnabled=e,this.updateSashEnablement())}get endSnappingEnabled(){return this._endSnappingEnabled}set endSnappingEnabled(e){this._endSnappingEnabled!==e&&(this._endSnappingEnabled=e,this.updateSashEnablement())}constructor(e,i={},r,l,d){var _,m;if(super(),this.onDidChange=void 0,this.onDidDragStart=void 0,this.onDidDragEnd=void 0,this.orientation=void 0,this.sashContainer=void 0,this.size=0,this.contentSize=0,this.proportions=void 0,this.viewItems=[],this.sashItems=[],this.sashDragState=void 0,this.proportionalLayout=void 0,this.getSashOrthogonalSize=void 0,this._startSnappingEnabled=!0,this._endSnappingEnabled=!0,this.onSashEnd=n=>{this.emit("sashchange",n),this.saveProportions();for(const o of this.viewItems)o.enabled=!0},this.orientation=(_=i.orientation)!=null?_:re.Vertical,this.proportionalLayout=(m=i.proportionalLayout)!=null?m:!0,this.getSashOrthogonalSize=i.getSashOrthogonalSize,r&&(this.onDidChange=r),l&&(this.onDidDragStart=l),d&&(this.onDidDragEnd=d),this.sashContainer=document.createElement("div"),this.sashContainer.classList.add("sash-container",Ci),e.prepend(this.sashContainer),i.descriptor){this.size=i.descriptor.size;for(const[n,o]of i.descriptor.views.entries()){const c=o.size,a=o.container,g=o.view;this.addView(a,g,c,n,!0)}this.contentSize=this.viewItems.reduce((n,o)=>n+o.size,0),this.saveProportions()}}addView(e,i,r,l=this.viewItems.length,d){let _;_=typeof r=="number"?r:r.type==="split"?this.getViewSize(r.index)/2:r.type==="invisible"?{cachedVisibleSize:r.cachedVisibleSize}:i.minimumSize;const m=this.orientation===re.Vertical?new Zi(e,i,_):new Ki(e,i,_);if(this.viewItems.splice(l,0,m),this.viewItems.length>1){const n=this.orientation===re.Vertical?new ii(this.sashContainer,{getHorizontalSashTop:a=>this.getSashPosition(a),getHorizontalSashWidth:this.getSashOrthogonalSize},{orientation:re.Horizontal}):new ii(this.sashContainer,{getVerticalSashLeft:a=>this.getSashPosition(a),getVerticalSashHeight:this.getSashOrthogonalSize},{orientation:re.Vertical}),o=this.orientation===re.Vertical?a=>({sash:n,start:a.startY,current:a.currentY}):a=>({sash:n,start:a.startX,current:a.currentX});n.on("start",a=>{var g;this.emit("sashDragStart"),this.onSashStart(o(a));const y=this.viewItems.map(v=>v.size);(g=this.onDidDragStart)==null||g.call(this,y)}),n.on("change",a=>this.onSashChange(o(a))),n.on("end",()=>{var a;this.emit("sashDragEnd"),this.onSashEnd(this.sashItems.findIndex(y=>y.sash===n));const g=this.viewItems.map(y=>y.size);(a=this.onDidDragEnd)==null||a.call(this,g)}),n.on("reset",()=>{const a=this.sashItems.findIndex(S=>S.sash===n),g=ie(a,-1,-1),y=ie(a+1,this.viewItems.length),v=this.findFirstSnapIndex(g),z=this.findFirstSnapIndex(y);(typeof v!="number"||this.viewItems[v].visible)&&(typeof z!="number"||this.viewItems[z].visible)&&this.emit("sashreset",a)});const c={sash:n};this.sashItems.splice(l-1,0,c)}d||this.relayout(),d||typeof r=="number"||r.type!=="distribute"||this.distributeViewSizes()}removeView(e,i){if(e<0||e>=this.viewItems.length)throw new Error("Index out of bounds");const r=this.viewItems.splice(e,1)[0].view;if(this.viewItems.length>=1){const l=Math.max(e-1,0);this.sashItems.splice(l,1)[0].sash.dispose()}return this.relayout(),i&&i.type==="distribute"&&this.distributeViewSizes(),r}moveView(e,i,r){const l=this.getViewCachedVisibleSize(i),d=l===void 0?this.getViewSize(i):ot.Invisible(l),_=this.removeView(i);this.addView(e,_,d,r)}getViewCachedVisibleSize(e){if(e<0||e>=this.viewItems.length)throw new Error("Index out of bounds");return this.viewItems[e].cachedVisibleSize}layout(e=this.size){const i=Math.max(this.size,this.contentSize);if(this.size=e,this.proportions)for(let r=0;r<this.viewItems.length;r++){const l=this.viewItems[r];l.size=oe(Math.round(this.proportions[r]*e),l.minimumSize,l.maximumSize)}else{const r=ie(0,this.viewItems.length),l=r.filter(_=>this.viewItems[_].priority===ne.Low),d=r.filter(_=>this.viewItems[_].priority===ne.High);this.resize(this.viewItems.length-1,e-i,void 0,l,d)}this.distributeEmptySpace(),this.layoutViews()}resizeView(e,i){if(e<0||e>=this.viewItems.length)return;const r=ie(0,this.viewItems.length).filter(m=>m!==e),l=[...r.filter(m=>this.viewItems[m].priority===ne.Low),e],d=r.filter(m=>this.viewItems[m].priority===ne.High),_=this.viewItems[e];i=Math.round(i),i=oe(i,_.minimumSize,Math.min(_.maximumSize,this.size)),_.size=i,this.relayout(l,d)}resizeViews(e){for(let i=0;i<e.length;i++){const r=this.viewItems[i];let l=e[i];l=Math.round(l),l=oe(l,r.minimumSize,Math.min(r.maximumSize,this.size)),r.size=l}this.contentSize=this.viewItems.reduce((i,r)=>i+r.size,0),this.saveProportions(),this.layout(this.size)}getViewSize(e){return e<0||e>=this.viewItems.length?-1:this.viewItems[e].size}isViewVisible(e){if(e<0||e>=this.viewItems.length)throw new Error("Index out of bounds");return this.viewItems[e].visible}setViewVisible(e,i){if(e<0||e>=this.viewItems.length)throw new Error("Index out of bounds");this.viewItems[e].setVisible(i),this.distributeEmptySpace(e),this.layoutViews(),this.saveProportions()}distributeViewSizes(){const e=[];let i=0;for(const m of this.viewItems)m.maximumSize-m.minimumSize>0&&(e.push(m),i+=m.size);const r=Math.floor(i/e.length);for(const m of e)m.size=oe(r,m.minimumSize,m.maximumSize);const l=ie(0,this.viewItems.length),d=l.filter(m=>this.viewItems[m].priority===ne.Low),_=l.filter(m=>this.viewItems[m].priority===ne.High);this.relayout(d,_)}dispose(){this.sashItems.forEach(e=>e.sash.dispose()),this.sashItems=[],this.sashContainer.remove()}relayout(e,i){const r=this.viewItems.reduce((l,d)=>l+d.size,0);this.resize(this.viewItems.length-1,this.size-r,void 0,e,i),this.distributeEmptySpace(),this.layoutViews(),this.saveProportions()}onSashStart({sash:e,start:i}){const r=this.sashItems.findIndex(l=>l.sash===e);(l=>{const d=this.viewItems.map(w=>w.size);let _,m,n=Number.NEGATIVE_INFINITY,o=Number.POSITIVE_INFINITY;const c=ie(r,-1,-1),a=ie(r+1,this.viewItems.length),g=c.reduce((w,I)=>w+(this.viewItems[I].minimumSize-d[I]),0),y=c.reduce((w,I)=>w+(this.viewItems[I].viewMaximumSize-d[I]),0),v=a.length===0?Number.POSITIVE_INFINITY:a.reduce((w,I)=>w+(d[I]-this.viewItems[I].minimumSize),0),z=a.length===0?Number.NEGATIVE_INFINITY:a.reduce((w,I)=>w+(d[I]-this.viewItems[I].viewMaximumSize),0);n=Math.max(g,z),o=Math.min(v,y);const S=this.findFirstSnapIndex(c),p=this.findFirstSnapIndex(a);if(typeof S=="number"){const w=this.viewItems[S],I=Math.floor(w.viewMinimumSize/2);_={index:S,limitDelta:w.visible?n-I:n+I,size:w.size}}if(typeof p=="number"){const w=this.viewItems[p],I=Math.floor(w.viewMinimumSize/2);m={index:p,limitDelta:w.visible?o+I:o-I,size:w.size}}this.sashDragState={start:l,current:l,index:r,sizes:d,minDelta:n,maxDelta:o,snapBefore:_,snapAfter:m}})(i)}onSashChange({current:e}){const{index:i,start:r,sizes:l,minDelta:d,maxDelta:_,snapBefore:m,snapAfter:n}=this.sashDragState;this.sashDragState.current=e;const o=e-r;this.resize(i,o,l,void 0,void 0,d,_,m,n),this.distributeEmptySpace(),this.layoutViews()}getSashPosition(e){let i=0;for(let r=0;r<this.sashItems.length;r++)if(i+=this.viewItems[r].size,this.sashItems[r].sash===e)return i;return 0}resize(e,i,r=this.viewItems.map(c=>c.size),l,d,_=Number.NEGATIVE_INFINITY,m=Number.POSITIVE_INFINITY,n,o){if(e<0||e>=this.viewItems.length)return 0;const c=ie(e,-1,-1),a=ie(e+1,this.viewItems.length);if(d)for(const h of d)qt(c,h),qt(a,h);if(l)for(const h of l)gt(c,h),gt(a,h);const g=c.map(h=>this.viewItems[h]),y=c.map(h=>r[h]),v=a.map(h=>this.viewItems[h]),z=a.map(h=>r[h]),S=c.reduce((h,O)=>h+(this.viewItems[O].minimumSize-r[O]),0),p=c.reduce((h,O)=>h+(this.viewItems[O].maximumSize-r[O]),0),w=a.length===0?Number.POSITIVE_INFINITY:a.reduce((h,O)=>h+(r[O]-this.viewItems[O].minimumSize),0),I=a.length===0?Number.NEGATIVE_INFINITY:a.reduce((h,O)=>h+(r[O]-this.viewItems[O].maximumSize),0),E=Math.max(S,I,_),F=Math.min(w,p,m);let J=!1;if(n){const h=this.viewItems[n.index],O=i>=n.limitDelta;J=O!==h.visible,h.setVisible(O,n.size)}if(!J&&o){const h=this.viewItems[o.index],O=i<o.limitDelta;J=O!==h.visible,h.setVisible(O,o.size)}if(J)return this.resize(e,i,r,l,d,_,m);for(let h=0,O=i=oe(i,E,F);h<g.length;h++){const Z=g[h],R=oe(y[h]+O,Z.minimumSize,Z.maximumSize);O-=R-y[h],Z.size=R}for(let h=0,O=i;h<v.length;h++){const Z=v[h],R=oe(z[h]-O,Z.minimumSize,Z.maximumSize);O+=R-z[h],Z.size=R}return i}distributeEmptySpace(e){const i=this.viewItems.reduce((o,c)=>o+c.size,0);let r=this.size-i;const l=ie(0,this.viewItems.length),d=[],_=l.filter(o=>this.viewItems[o].priority===ne.Low),m=l.filter(o=>this.viewItems[o].priority===ne.Normal),n=l.filter(o=>this.viewItems[o].priority===ne.High);d.push(...n,...m,..._),typeof e=="number"&&gt(d,e);for(let o=0;r!==0&&o<d.length;o++){const c=this.viewItems[d[o]],a=oe(c.size+r,c.minimumSize,c.maximumSize);r-=a-c.size,c.size=a}}layoutViews(){var e;this.contentSize=this.viewItems.reduce((r,l)=>r+l.size,0);let i=0;for(const r of this.viewItems)r.layout(i),i+=r.size;(e=this.onDidChange)!=null&&e.call(this,this.viewItems.map(r=>r.size)),this.sashItems.forEach(r=>r.sash.layout()),this.updateSashEnablement()}saveProportions(){this.proportionalLayout&&this.contentSize>0&&(this.proportions=this.viewItems.map(e=>e.size/this.contentSize))}updateSashEnablement(){let e=!1;const i=this.viewItems.map(n=>e=n.size-n.minimumSize>0||e);e=!1;const r=this.viewItems.map(n=>e=n.maximumSize-n.size>0||e),l=[...this.viewItems].reverse();e=!1;const d=l.map(n=>e=n.size-n.minimumSize>0||e).reverse();e=!1;const _=l.map(n=>e=n.maximumSize-n.size>0||e).reverse();let m=0;for(let n=0;n<this.sashItems.length;n++){const{sash:o}=this.sashItems[n];m+=this.viewItems[n].size;const c=!(i[n]&&_[n+1]),a=!(r[n]&&d[n+1]);if(c&&a){const g=ie(n,-1,-1),y=ie(n+1,this.viewItems.length),v=this.findFirstSnapIndex(g),z=this.findFirstSnapIndex(y),S=typeof v=="number"&&!this.viewItems[v].visible,p=typeof z=="number"&&!this.viewItems[z].visible;S&&d[n]&&(m>0||this.startSnappingEnabled)?o.state=ee.Minimum:p&&i[n]&&(m<this.contentSize||this.endSnappingEnabled)?o.state=ee.Maximum:o.state=ee.Disabled}else o.state=c&&!a?ee.Minimum:!c&&a?ee.Maximum:ee.Enabled}}findFirstSnapIndex(e){for(const i of e){const r=this.viewItems[i];if(r.visible&&r.snap)return i}for(const i of e){const r=this.viewItems[i];if(r.visible&&r.maximumSize-r.minimumSize>0)return;if(!r.visible&&r.snap)return i}}}class Pe{constructor(e){this.size=void 0,this.size=e}getPreferredSize(){return this.size}}class ni{constructor(e,i){this.proportion=void 0,this.layoutService=void 0,this.proportion=e,this.layoutService=i}getPreferredSize(){return this.proportion*this.layoutService.getSize()}}class st{getPreferredSize(){}}class ri{get preferredSize(){return this.layoutStrategy.getPreferredSize()}set preferredSize(e){if(typeof e=="number")this.layoutStrategy=new Pe(e);else if(typeof e=="string"){const i=e.trim();if(rt(i,"%")){const r=Number(i.slice(0,-1))/100;this.layoutStrategy=new ni(r,this.layoutService)}else if(rt(i,"px")){const r=Number(i.slice(0,-2))/100;this.layoutStrategy=new Pe(r)}else if(typeof Number.parseFloat(i)=="number"){const r=Number.parseFloat(i);this.layoutStrategy=new Pe(r)}else this.layoutStrategy=new st}else this.layoutStrategy=new st}constructor(e,i){var r;if(this.minimumSize=0,this.maximumSize=Number.POSITIVE_INFINITY,this.element=void 0,this.priority=void 0,this.snap=void 0,this.layoutService=void 0,this.layoutStrategy=void 0,this.layoutService=e,this.element=i.element,this.minimumSize=typeof i.minimumSize=="number"?i.minimumSize:30,this.maximumSize=typeof i.maximumSize=="number"?i.maximumSize:Number.POSITIVE_INFINITY,typeof i.preferredSize=="number")this.layoutStrategy=new Pe(i.preferredSize);else if(typeof i.preferredSize=="string"){const l=i.preferredSize.trim();if(rt(l,"%")){const d=Number(l.slice(0,-1))/100;this.layoutStrategy=new ni(d,this.layoutService)}else if(rt(l,"px")){const d=Number(l.slice(0,-2));this.layoutStrategy=new Pe(d)}else if(typeof Number.parseFloat(l)=="number"){const d=Number.parseFloat(l);this.layoutStrategy=new Pe(d)}else this.layoutStrategy=new st}else this.layoutStrategy=new st;this.priority=(r=i.priority)!=null?r:ne.Normal,this.snap=typeof i.snap=="boolean"&&i.snap}layout(e){}}function si(u){return u.minSize!==void 0||u.maxSize!==void 0||u.preferredSize!==void 0||u.priority!==void 0||u.visible!==void 0}const It=L.forwardRef(({className:u,children:e},i)=>ve.createElement("div",{ref:i,className:zt("split-view-view",oi,u)},e));It.displayName="Allotment.Pane";const mi=L.forwardRef(({children:u,className:e,id:i,maxSize:r=1/0,minSize:l=30,proportionalLayout:d=!0,separator:_=!0,sizes:m,defaultSizes:n=m,snap:o=!1,vertical:c=!1,onChange:a,onReset:g,onVisibleChange:y,onDragStart:v,onDragEnd:z},S)=>{const p=L.useRef(null),w=L.useRef([]),I=L.useRef(new Map),E=L.useRef(null),F=L.useRef(new Map),J=L.useRef(new Hi),h=L.useRef([]),[O,Z]=L.useState(!1),R=L.useMemo(()=>ve.Children.toArray(u).filter(ve.isValidElement),[u]),ae=L.useCallback(N=>{var D,$;const M=(D=h.current)==null?void 0:D[N];return typeof M?.preferredSize=="number"&&(($=E.current)!=null&&$.resizeView(N,Math.round(M.preferredSize)),!0)},[]);return L.useImperativeHandle(S,()=>({reset:()=>{if(g)g();else{var N;(N=E.current)==null||N.distributeViewSizes();for(let D=0;D<h.current.length;D++)ae(D)}},resize:N=>{var D;(D=E.current)==null||D.resizeViews(N)}})),Xt(()=>{let N=!0;n&&F.current.size!==n.length&&(N=!1,console.warn(`Expected ${n.length} children based on defaultSizes but found ${F.current.size}`)),N&&n&&(w.current=R.map(M=>M.key));const D=He({orientation:c?re.Vertical:re.Horizontal,proportionalLayout:d},N&&n&&{descriptor:{size:n.reduce((M,P)=>M+P,0),views:n.map((M,P)=>{var T,G,Se,pe;const K=I.current.get(w.current[P]),ye=new ri(J.current,He({element:document.createElement("div"),minimumSize:(T=K?.minSize)!=null?T:l,maximumSize:(G=K?.maxSize)!=null?G:r,priority:(Se=K?.priority)!=null?Se:ne.Normal},K?.preferredSize&&{preferredSize:K?.preferredSize},{snap:(pe=K?.snap)!=null?pe:o}));return h.current.push(ye),{container:[...F.current.values()][P],size:M,view:ye}})}});E.current=new Qi(p.current,D,a,v,z),E.current.on("sashDragStart",()=>{var M;(M=p.current)==null||M.classList.add("split-view-sash-dragging")}),E.current.on("sashDragEnd",()=>{var M;(M=p.current)==null||M.classList.remove("split-view-sash-dragging")}),E.current.on("sashchange",M=>{if(y&&E.current){const P=R.map(T=>T.key);for(let T=0;T<P.length;T++){const G=I.current.get(P[T]);G?.visible!==void 0&&G.visible!==E.current.isViewVisible(T)&&y(T,E.current.isViewVisible(T))}}}),E.current.on("sashreset",M=>{if(g)g();else{var P;if(ae(M)||ae(M+1))return;(P=E.current)==null||P.distributeViewSizes()}});const $=E.current;return()=>{$.dispose()}},[]),Xt(()=>{if(O){const q=R.map(x=>x.key),le=[...w.current],ce=q.filter(x=>!w.current.includes(x)),De=q.filter(x=>w.current.includes(x)),Le=w.current.map(x=>!q.includes(x));for(let x=Le.length-1;x>=0;x--){var N;Le[x]&&((N=E.current)!=null&&N.removeView(x),le.splice(x,1),h.current.splice(x,1))}for(const x of ce){var D,$,M,P,T;const V=I.current.get(x),H=new ri(J.current,He({element:document.createElement("div"),minimumSize:(D=V?.minSize)!=null?D:l,maximumSize:($=V?.maxSize)!=null?$:r,priority:(M=V?.priority)!=null?M:ne.Normal},V?.preferredSize&&{preferredSize:V?.preferredSize},{snap:(P=V?.snap)!=null?P:o}));(T=E.current)!=null&&T.addView(F.current.get(x),H,ot.Distribute,q.findIndex(B=>B===x)),le.splice(q.findIndex(B=>B===x),0,x),h.current.splice(q.findIndex(B=>B===x),0,H)}for(;!Mi(q,le);)for(const[x,V]of q.entries()){const H=le.findIndex(B=>B===V);if(H!==x){var G;(G=E.current)==null||G.moveView(F.current.get(V),H,x);const B=le[H];le.splice(H,1),le.splice(x,0,B);break}}for(const x of ce){var Se;const V=q.findIndex(B=>B===x),H=h.current[V].preferredSize;H!==void 0&&((Se=E.current)==null||Se.resizeView(V,H))}for(const x of[...ce,...De]){var pe,K;const V=I.current.get(x),H=q.findIndex(B=>B===x);V&&si(V)&&V.visible!==void 0&&((pe=E.current)==null?void 0:pe.isViewVisible(H))!==V.visible&&((K=E.current)==null||K.setViewVisible(H,V.visible))}for(const x of De){const V=I.current.get(x),H=q.findIndex(B=>B===x);if(V&&si(V)){var ye;V.preferredSize!==void 0&&h.current[H].preferredSize!==V.preferredSize&&(h.current[H].preferredSize=V.preferredSize);let B=!1;V.minSize!==void 0&&h.current[H].minimumSize!==V.minSize&&(h.current[H].minimumSize=V.minSize,B=!0),V.maxSize!==void 0&&h.current[H].maximumSize!==V.maxSize&&(h.current[H].maximumSize=V.maxSize,B=!0),B&&((ye=E.current)==null||ye.layout())}}(ce.length>0||Le.length>0)&&(w.current=q)}},[R,O,r,l,o]),L.useEffect(()=>{E.current&&(E.current.onDidChange=a)},[a]),L.useEffect(()=>{E.current&&(E.current.onDidDragStart=v)},[v]),L.useEffect(()=>{E.current&&(E.current.onDidDragEnd=z)},[z]),Pi({ref:p,onResize:({width:N,height:D})=>{var $;N&&D&&(($=E.current)!=null&&$.layout(c?D:N),J.current.setSize(c?D:N),Z(!0))}}),L.useEffect(()=>{li&&qi(20)},[]),ve.createElement("div",{ref:p,className:zt("split-view",c?"split-view-vertical":"split-view-horizontal",{"split-view-separator-border":_},Ai,c?Fi:ki,{[Ri]:_},e),id:i},ve.createElement("div",{className:zt("split-view-container",Ti)},ve.Children.toArray(u).map(N=>{if(!ve.isValidElement(N))return null;const D=N.key;return N.type.displayName==="Allotment.Pane"?(I.current.set(D,N.props),ve.cloneElement(N,{key:D,ref:$=>{const M=N.ref;M&&(M.current=$),$?F.current.set(D,$):F.current.delete(D)}})):ve.createElement(It,{key:D,ref:$=>{$?F.current.set(D,$):F.current.delete(D)}},N)})))});function qi(u){const e=oe(u,4,20),i=oe(u,1,8);document.documentElement.style.setProperty("--sash-size",e+"px"),document.documentElement.style.setProperty("--sash-hover-size",i+"px"),(function(r){ci=r,hi.emit("onDidChangeGlobalSize",r)})(e)}mi.displayName="Allotment";var tn=Object.assign(mi,{Pane:It});export{tn as x};
