# study

## monorepo 中的项目 ts 文件找不到，eslint 报错

解决方案：在根目录下的 eslint.config.js 文件中添加以下代码，找到对应的 ts 配置文件

```ts
languageOptions: {
  parserOptions: {
    tsConfigRootDir: path.resolve(__dirname),
    project: ["./apps/*/tsconfig.json"],
  },
},
```

## 把一段 JS 代码，用 URL.createObjectURL 和 new Blob 的方式变为一个 url

```js
// code 为一段 JS 代码字符串
URL.createObjectURL(new Blob([code], { type: "application/javascript" }));
```

## @monaco-editor/react 版本

针对 react19 版本，需要安装 `pnpm add @monaco-editor/react@next` 版本


## react 插件有什么用吗

@vitejs/plugin-react 为什么注释了，也有效呢

## qiankun

- 子应用中的图片路径是相对路径，当子应用被加载到主应用中时
- 两套解析规则：
  - 模块里用的路径解析（ESM）: import/动态 import 在浏览器里是按“模块自身的 baseURL”解析的；
  - DOM/CSS 的 URL 解析: 给 <img src>、link.href、CSS url(...)、new Worker('...') 这类“运行时字符串 URL”时，浏览器会按“当前文档的 baseURL”

### 项目端口

- 主应用：6000
- 子应用：6001
