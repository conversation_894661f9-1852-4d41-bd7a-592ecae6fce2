<script setup lang="ts">
import { ElRow, ElCol, ElCard, ElTimeline, ElTimelineItem } from "element-plus";
import { MoreFilled } from "@element-plus/icons-vue";
import { ref } from "vue";
// import mainQiankunApp from "./qiankunApp";
import "./qiankun";

const subProjectList = ref([
  {
    title: "React Playground",
    desc: "在线编辑，在线预览",
    path: import.meta.env.VITE_REACT_PLAYGROUND_ROUTE_PATH,
    appName: "react-playground",
  },
  {
    title: "乾坤通信",
    desc: "主应用与微应用之间的联系",
    path: "/qiankun-communication",
    appName: "qiankun-communication",
  },
]);

const loadProject = (path: string) => {
  // mainQiankunApp.navigateTo(path);
  console.log("path======>", path, history);
  history.pushState({}, "", path);
};
</script>

<template>
  <div class="app">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="card-common">
          <template #header>
            <div>
              <span>Copyer 项目集合</span>
            </div>
          </template>
          <div class="card-body">
            <el-timeline>
              <el-timeline-item
                v-for="item in subProjectList"
                :key="item.title"
                color="#0bbd87"
                center
                placement="top"
              >
                <el-card class="timeline-card" @click="loadProject(item.path)">
                  <h4>{{ item.title }}</h4>
                  <p>{{ item.desc }}</p>
                </el-card>
              </el-timeline-item>
              <el-timeline-item type="primary" :icon="MoreFilled">
                敬请期待更多...
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card class="card-common">
          <template #header>
            <div style="text-align: right">
              <span>项目演示</span>
            </div>
          </template>
          <div class="card-body">
            <div id="__qiankun_container"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped>
.app {
  height: 100vh;
  width: 100vw;
  padding: 10px;
  box-sizing: border-box;
}
.card-left {
  height: 100%;
  overflow: visible;
}
.card-common :deep(.el-card__header) {
  font-size: 20px;
  height: 60px;
  line-height: 60px;
  background-color: #64b687;
  color: #fff;
  font-weight: 700;
  padding: 0 20px 0 20px;
  user-select: none;
  box-sizing: border-box;
}
.card-common ::deep(.el-card__body) {
  padding: 10px;
  box-sizing: border-box;
}
.card-body {
  height: calc(100vh - 120px);
  overflow-y: auto;
}

.qiankun-container {
  height: 100%;
  min-height: 400px;
  position: relative;
}
.timeline-card {
  transition: all 0.3s ease;
}

.timeline-card:hover {
  background-color: #64b68720;
  cursor: pointer;
}

.timeline-card.active {
  background-color: #64b68730;
  border-color: #64b687;
  box-shadow: 0 2px 8px rgba(100, 182, 135, 0.3);
}

.timeline-card:hover * {
  /* 重置内部元素的 hover 效果 */
  background-color: transparent;
}

.default-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #666;
}

.welcome-text {
  text-align: center;
}

/* 微应用加载样式 */
:deep(.micro-app-loading) {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #666;
}

:deep(.loading-spinner) {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #64b687;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

:deep(.loading-text) {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

:deep(.loading-detail) {
  font-size: 12px;
  color: #999;
  margin-bottom: 16px;
  text-align: center;
  max-width: 300px;
}

:deep(.loading-progress) {
  width: 200px;
  height: 3px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

:deep(.loading-progress-bar) {
  height: 100%;
  background: linear-gradient(90deg, #64b687, #4a9d6a);
  width: 0%;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

:deep(.micro-app-error) {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #f56c6c;
  text-align: center;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
