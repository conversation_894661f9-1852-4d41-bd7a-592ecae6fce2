.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  border-bottom: 1px solid #ddd;
  color: var(--react-playground-text);
  padding-left: 10px;
  background-color: var(--react-playground-bg);
  padding: 0 20px;
  
}

.logo {
  display: flex;
  align-items: center;
  flex: 1;
  :global {
      .title {
        margin-left: 10px;
        font-weight: 500;
      }
    }
}

.link {
  display: flex;
  justify-content: flex-end;
  flex: 1;
  :global {
    .icon {
      width: 24px;
      height: 24px;
      margin-left: 10px;
      cursor: pointer;
    }
  }
}