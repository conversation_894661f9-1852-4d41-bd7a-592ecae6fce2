<script setup lang="ts">
import { ElRow, ElCol, ElCard, ElTimeline, ElTimelineItem } from "element-plus";
import { MoreFilled } from "@element-plus/icons-vue";
import { ref } from "vue";
// import mainQiankunApp from "./qiankunApp";
import "./qiankun";

const subProjectList = ref([
  {
    title: "React Playground",
    desc: "在线编辑，在线预览",
    path: import.meta.env.VITE_REACT_PLAYGROUND_ROUTE_PATH,
    appName: "react-playground",
  },
  {
    title: "乾坤通信",
    desc: "主应用与微应用之间的联系",
    path: "/qiankun-communication",
    appName: "qiankun-communication",
  },
]);

const loadProject = (path: string) => {
  // mainQiankunApp.navigateTo(path);
  console.log("path======>", path, history);
  history.pushState({}, "", path);
};
</script>

<template>
  <div class="app">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="card-common">
          <template #header>
            <div>
              <span>Copyer 项目集合</span>
            </div>
          </template>
          <div class="card-body">
            <el-timeline>
              <el-timeline-item
                v-for="item in subProjectList"
                :key="item.title"
                color="#0bbd87"
                center
                placement="top"
              >
                <el-card class="timeline-card" @click="loadProject(item.path)">
                  <h4>{{ item.title }}</h4>
                  <p>{{ item.desc }}</p>
                </el-card>
              </el-timeline-item>
              <el-timeline-item type="primary" :icon="MoreFilled">
                敬请期待更多...
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card class="card-common">
          <template #header>
            <div style="text-align: right">
              <span>项目演示</span>
            </div>
          </template>
          <div class="card-body">
            <div id="__qiankun_container">
              <!-- 默认展示内容 -->
              <div class="default-welcome">
                <div class="welcome-icon">
                  <svg
                    width="80"
                    height="80"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 2L2 7L12 12L22 7L12 2Z"
                      stroke="#64b687"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M2 17L12 22L22 17"
                      stroke="#64b687"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M2 12L12 17L22 12"
                      stroke="#64b687"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
                <h2 class="welcome-title">欢迎使用 Copyer 项目集合</h2>
                <p class="welcome-description">
                  这里是一个基于乾坤微前端架构的项目展示平台
                </p>
                <div class="welcome-features">
                  <div class="feature-item">
                    <div class="feature-icon">🚀</div>
                    <span>微前端架构</span>
                  </div>
                  <div class="feature-item">
                    <div class="feature-icon">⚡</div>
                    <span>快速加载</span>
                  </div>
                  <div class="feature-item">
                    <div class="feature-icon">🎯</div>
                    <span>独立部署</span>
                  </div>
                </div>
                <div class="welcome-action">
                  <div class="action-arrow">👈</div>
                  <span class="action-text">点击左侧项目开始体验</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<style scoped>
.app {
  height: 100vh;
  width: 100vw;
  padding: 10px;
  box-sizing: border-box;
}
.card-left {
  height: 100%;
  overflow: visible;
}
.card-common :deep(.el-card__header) {
  font-size: 20px;
  height: 60px;
  line-height: 60px;
  background-color: #64b687;
  color: #fff;
  font-weight: 700;
  padding: 0 20px 0 20px;
  user-select: none;
  box-sizing: border-box;
}
.card-common ::deep(.el-card__body) {
  padding: 10px;
  box-sizing: border-box;
}
.card-body {
  height: calc(100vh - 120px);
  overflow-y: auto;
}

.qiankun-container {
  height: 100%;
  min-height: 400px;
  position: relative;
}
.timeline-card {
  transition: all 0.3s ease;
}

.timeline-card:hover {
  background-color: #64b68720;
  cursor: pointer;
}

.timeline-card.active {
  background-color: #64b68730;
  border-color: #64b687;
  box-shadow: 0 2px 8px rgba(100, 182, 135, 0.3);
}

.timeline-card:hover * {
  /* 重置内部元素的 hover 效果 */
  background-color: transparent;
}

/* 默认欢迎页面样式 */
.default-welcome {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 500px;
  padding: 40px 20px;
  text-align: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.default-welcome::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(100, 182, 135, 0.1) 0%,
    transparent 70%
  );
  animation: float 6s ease-in-out infinite;
}

.welcome-icon {
  margin-bottom: 24px;
  animation: bounce 2s ease-in-out infinite;
  z-index: 1;
  position: relative;
}

.welcome-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
  z-index: 1;
  position: relative;
}

.welcome-description {
  font-size: 16px;
  color: #7f8c8d;
  margin-bottom: 32px;
  line-height: 1.6;
  max-width: 400px;
  z-index: 1;
  position: relative;
}

.welcome-features {
  display: flex;
  gap: 24px;
  margin-bottom: 40px;
  z-index: 1;
  position: relative;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  min-width: 100px;
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.feature-item span {
  font-size: 14px;
  color: #34495e;
  font-weight: 500;
}

.welcome-action {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: rgba(100, 182, 135, 0.1);
  border: 2px dashed #64b687;
  border-radius: 12px;
  z-index: 1;
  position: relative;
}

.action-arrow {
  font-size: 20px;
  animation: pulse 2s ease-in-out infinite;
}

.action-text {
  font-size: 16px;
  color: #64b687;
  font-weight: 500;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes float {
  0%,
  100% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 微应用加载样式 */
:deep(.micro-app-loading) {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #666;
}

:deep(.loading-spinner) {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #64b687;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

:deep(.loading-text) {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

:deep(.loading-detail) {
  font-size: 12px;
  color: #999;
  margin-bottom: 16px;
  text-align: center;
  max-width: 300px;
}

:deep(.loading-progress) {
  width: 200px;
  height: 3px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

:deep(.loading-progress-bar) {
  height: 100%;
  background: linear-gradient(90deg, #64b687, #4a9d6a);
  width: 0%;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

:deep(.micro-app-error) {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #f56c6c;
  text-align: center;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
