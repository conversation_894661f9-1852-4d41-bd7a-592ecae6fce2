import React, { useContext } from "react";
import { PlaygroundContext } from "../../playgroundContext";
import styles from "./index.module.scss";
import copy from "copy-to-clipboard";
import {
  logo,
  themeDark,
  themeLight,
  downloadDark,
  downloadLight,
  shareDark,
  shareLight,
} from "../../assets/svgs";
import { downloadFiles } from "../../helper/utils";
const Header: React.FC = () => {
  const { theme, setTheme, files } = useContext(PlaygroundContext);
  return (
    <div className={styles.header}>
      <div className={styles.logo}>
        <img src={logo} alt="logo" />
        <div className="title">React PlayGround</div>
      </div>
      <div className={styles.link}>
        <img
          className="icon"
          src={theme !== "light" ? themeDark : themeLight}
          alt="theme"
          onClick={() => setTheme(theme === "light" ? "dark" : "light")}
        />
        <img
          className="icon"
          src={theme !== "light" ? downloadDark : downloadLight}
          alt="download"
          onClick={async () => {
            await downloadFiles(files);
          }}
        />
        <img
          className="icon"
          src={theme !== "light" ? shareDark : shareLight}
          alt="share"
          onClick={() => {
            copy(window.location.href);
            alert("分享链接已复制。");
          }}
        />
      </div>
    </div>
  );
};

export default Header;
