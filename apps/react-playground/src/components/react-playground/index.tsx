import { useContext } from "react";
import { Allotment } from "allotment";
import "allotment/dist/style.css";
import Header from "./layout/header";
import CodeEditor from "./layout/code-editor";
import Preview from "./layout/preview";
import { PlaygroundProvider, PlaygroundContext } from "./playgroundContext";
import "./index.css";

function InnerPlayground() {
  const { theme } = useContext(PlaygroundContext);
  return (
    <div className={`react_playground_${theme}`} style={{ height: "100vh" }}>
      <Header />
      <div style={{ height: "calc(100vh - 60px)" }}>
        <Allotment defaultSizes={[100, 100]}>
          <Allotment.Pane minSize={500}>
            <CodeEditor />
          </Allotment.Pane>
          <Allotment.Pane minSize={0}>
            <Preview />
          </Allotment.Pane>
        </Allotment>
      </div>
    </div>
  );
}

export default function ReactPlayground() {
  return (
    <PlaygroundProvider>
      <InnerPlayground />
    </PlaygroundProvider>
  );
}
