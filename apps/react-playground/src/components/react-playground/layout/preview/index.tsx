import { useContext, useState, useEffect } from "react";
import { PlaygroundContext } from "../../playgroundContext";
import Message from "../../components/message";
import iframeRaw from "../../assets/iframe.html?raw";
import { IMPORT_MAP_FILE_NAME } from "../../helper/file";
// import CompilerWorker from "./compiler.worker?worker";
import { compile, checkReactCompatibility } from './compiler'
import { debounce } from "lodash-es";

interface MessageData {
  data: {
    type: string;
    message: string;
  };
}

export function Preview() {
  const { files } = useContext(PlaygroundContext);
  const [compilerResult, setCompilerResult] = useState("");
  const [iframeUrl, setIframeUrl] = useState("");
  const [error, setError] = useState("");
  const [compatibilityError, setCompatibilityError] = useState("");

  // qiankun是加载不出来
  // useEffect(() => {
  //   if (!compilerWorkerRef.current) {
  //     // compilerWorkerRef.current = new CompilerWorker();
  //     compilerWorkerRef.current = new Worker(
  //       new URL("./compiler.worker.ts", import.meta.url),
  //       { type: "module" }
  //     );
  //     // 主线程接收 worker 线程的消息
  //     compilerWorkerRef.current.addEventListener("message", (data) => {
  //       const { type, data: codeData } = data.data;
  //       if (type === "ok") {
  //         setCompilerResult(codeData);
  //       } else {
  //         console.log("data", data);
  //       }
  //     });
  //   }
  // }, []);

  useEffect(
    debounce(() => {
      try {
        // 检查 React 兼容性
        if (!checkReactCompatibility(files)) {
          setCompatibilityError("React 版本兼容性检查失败，请确保使用正确的版本");
          return;
        }
        
        setCompatibilityError("");
        // 主线程传递给 worker 线程
        // compilerWorkerRef.current?.postMessage(files);
        setCompilerResult(compile(files))
      } catch (error) {
        console.error("编译失败:", error);
        setError(`编译失败: ${error instanceof Error ? error.message : String(error)}`);
      }
    }, 500),
    [files]
  );

  const getIframeUrl = () => {
    const res = iframeRaw
      .replace(
        '<script type="importmap"></script>',
        `<script type="importmap">${files[IMPORT_MAP_FILE_NAME].value}</script>`
      )
      .replace(
        '<script type="module" id="appSrc"></script>',
        `<script type="module" id="appSrc">${compilerResult}</script>`
      );
    return URL.createObjectURL(new Blob([res], { type: "text/html" }));
  };

  useEffect(() => {
    if (compilerResult && !compatibilityError) {
      setIframeUrl(getIframeUrl());
    }
  }, [files[IMPORT_MAP_FILE_NAME].value, compilerResult, compatibilityError]);

  const handlePostMessage = (msg: MessageData) => {
    if (msg.data.type === "error") {
      setError(msg.data.message);
    }
  };

  useEffect(() => {
    window.addEventListener("message", handlePostMessage);
    return () => {
      window.removeEventListener("message", handlePostMessage);
    };
  }, []);

  return (
    <div style={{ height: "100%", position: "relative" }}>
      {/* 查看实际效果 */}
      {iframeUrl && !compatibilityError ? (
        <iframe
          src={iframeUrl}
          style={{
            width: "100%",
            height: "100%",
            padding: 0,
            border: "none",
          }}
        />
      ) : compatibilityError ? (
        <div style={{ 
          padding: "20px", 
          textAlign: "center", 
          color: "#ff4d4f",
          backgroundColor: "#fff2f0",
          border: "1px solid #ffccc7",
          borderRadius: "6px",
          margin: "20px"
        }}>
          <h3>兼容性错误</h3>
          <p>{compatibilityError}</p>
          <p>请检查 import-map.json 中的 React 版本配置</p>
        </div>
      ) : (
        <div>编译中...</div>
      )}
      <Message type="error" content={error} />
      {/* 查看编译结果后的代码 */}
      {/* <Editor
        file={{
          name: "dist.js",
          value: compilerResult,
          language: "javascript",
        }}
      /> */}
    </div>
  );
}

export default Preview;
