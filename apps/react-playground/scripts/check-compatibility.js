#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 检查 React 兼容性
 */
async function checkReactCompatibility() {
  console.log('🔍 检查 React 兼容性...');
  
  try {
    // 读取 package.json
    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // 读取 import-map.json
    const importMapPath = path.join(__dirname, '..', 'src', 'components', 'react-playground', 'helper', 'temp', 'import-map.json');
    const importMap = JSON.parse(fs.readFileSync(importMapPath, 'utf8'));
    
    // 检查 React 版本
    const localReactVersion = packageJson.dependencies.react;
    const cdnReactVersion = importMap.imports.react.match(/react@([^/]+)/)?.[1];
    
    console.log(`📦 本地 React 版本: ${localReactVersion}`);
    console.log(`🌐 CDN React 版本: ${cdnReactVersion}`);
    
    if (localReactVersion !== cdnReactVersion) {
      console.warn('⚠️  警告: 本地和 CDN React 版本不一致');
      console.warn('   这可能导致生产环境兼容性问题');
    }
    
    // 检查必要的导出
    const requiredExports = [
      'react',
      'react-dom/client',
      'react/memo',
      'react/useState',
      'react/useEffect',
      'react/useContext'
    ];
    
    const missingExports = requiredExports.filter(exportName => !importMap.imports[exportName]);
    
    if (missingExports.length > 0) {
      console.error('❌ 缺少必要的导出:', missingExports);
      process.exit(1);
    }
    
    console.log('✅ React 兼容性检查通过');
    
  } catch (error) {
    console.error('❌ 兼容性检查失败:', error.message);
    process.exit(1);
  }
}

// 运行检查
checkReactCompatibility();
