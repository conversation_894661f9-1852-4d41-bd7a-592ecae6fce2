<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>React PlayGround</title>
    <style>
      /* 子应用加载动画 - 只在容器内显示 */
      #root {
        height: 100%;
        min-height: 600px;
      }

      .app-loading {
        margin-top: 100px;
        position: relative;
        width: 100%;
        height: 100%;
        background: #fff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .loading-dots {
        display: flex;
        gap: 8px;
        margin-bottom: 20px;
      }

      .loading-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #007acc;
        animation: bounce 1.4s ease-in-out infinite both;
      }

      .loading-dot:nth-child(1) {
        animation-delay: -0.32s;
      }
      .loading-dot:nth-child(2) {
        animation-delay: -0.16s;
      }
      .loading-dot:nth-child(3) {
        animation-delay: 0s;
      }

      .loading-text {
        color: #000;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        font-size: 16px;
        margin-bottom: 8px;
      }

      .loading-detail {
        color: #999;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        font-size: 12px;
        text-align: center;
        max-width: 300px;
        line-height: 1.4;
      }

      @keyframes bounce {
        0%,
        80%,
        100% {
          transform: scale(0);
          opacity: 0.3;
        }
        40% {
          transform: scale(1);
          opacity: 1;
        }
      }

      /* 当应用加载完成后隐藏 loading */
      .app-loaded .app-loading {
        display: none;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- 子应用加载动画 -->
      <div class="app-loading">
        <div class="loading-dots">
          <div class="loading-dot"></div>
          <div class="loading-dot"></div>
          <div class="loading-dot"></div>
        </div>
        <div class="loading-text">正在加载 React Playground...</div>
        <div class="loading-detail">
          Monaco Editor、TypeScript 编译器等大型资源加载中，请稍候
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
