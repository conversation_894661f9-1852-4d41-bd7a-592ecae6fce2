export type Theme = "dark" | "light";

export interface File {
  /** 文件名 */
  name: string;
  /** 文件内容 */
  value: string;
  /** 文件类型 */
  language: string;
}

export interface Files {
  [key: string]: File;
}
export interface PlaygroundProviderProps {
  /** 主题 */
  theme: Theme;
  /** 设置主题 */
  setTheme: (theme: Theme) => void;
  /** 所有文件 */
  files: Files;
  /** 选中的文件名 */
  selectedFileName: string;
  /** 设置选中的文件 */
  setSelectedFileName: (fileName: string) => void;
  /** 添加文件 */
  addFile: (file: string) => void;
  /** 设置所有文件 */
  setFiles: (files: Files) => void;
  /** 删除文件 */
  removeFile: (fileName: string) => void;
  /** 更新文件名 */
  updateFileName: (oldFieldName: string, newFieldName: string) => void;
}
