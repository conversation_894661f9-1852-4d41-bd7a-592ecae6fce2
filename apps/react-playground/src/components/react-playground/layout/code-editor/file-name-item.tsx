import React, { useState, useRef } from "react";
import cs from "classnames";
import styles from "./index.module.scss";

export interface FileNameItemProps {
  value: string;
  selected: boolean;
  onClick: () => void;
  /** 编辑完成回调 */
  onEditComplete?: (value: string) => void;
  /** 是否是新创建的文件，是就处于编辑状态 */
  created?: boolean;
  onRemove?: () => void;
  /** 是否只读 */
  readonly?: boolean;
}

const FileNameItem: React.FC<FileNameItemProps> = (props) => {
  const {
    value,
    selected,
    onClick,
    onEditComplete,
    created,
    onRemove,
    readonly,
  } = props;
  const [name, setName] = useState(value);
  const [editing, setEditing] = useState(created);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleDoubleClick = () => {
    if (readonly) return;
    setEditing(true);
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  };

  const handleFileRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!confirm("确定删除吗？")) return;
    onRemove?.();
  };

  return (
    <div
      onClick={onClick}
      className={cs(styles.tabItem, { [styles.selectTabItem]: selected })}
    >
      {editing ? (
        <input
          ref={inputRef}
          value={name}
          className={styles.tabsItemInput}
          onChange={(e) => setName(e.target.value)}
          onBlur={() => {
            setEditing(false);
            onEditComplete?.(name);
          }}
        />
      ) : (
        <>
          <span onDoubleClick={handleDoubleClick}>{name}</span>
          {!readonly && (
            <span
              style={{ marginLeft: 5, display: "flex" }}
              onClick={handleFileRemove}
            >
              <svg width="12" height="12" viewBox="0 0 24 24">
                <line stroke="#999" x1="18" y1="6" x2="6" y2="18"></line>
                <line stroke="#999" x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </span>
          )}
        </>
      )}
    </div>
  );
};

export default FileNameItem;
