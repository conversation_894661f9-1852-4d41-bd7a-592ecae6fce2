import type { Files } from "../types";
import importMap from "./temp/import-map.json?raw";
import AppCss from "./temp/App.css?raw";
import App from "./temp/App.tsx?raw";
import main from "./temp/main.tsx?raw";
import { fileNameToLanguage } from "./utils";

// app 文件名
export const APP_COMPONENT_FILE_NAME = "App.tsx";
// esm 模块映射文件名
export const IMPORT_MAP_FILE_NAME = "import-map.json";
// app 入口文件名
export const ENTRY_FILE_NAME = "main.tsx";

export const initFiles: Files = {
  [ENTRY_FILE_NAME]: {
    name: ENTRY_FILE_NAME,
    language: fileNameToLanguage(ENTRY_FILE_NAME),
    value: main,
  },
  [APP_COMPONENT_FILE_NAME]: {
    name: APP_COMPONENT_FILE_NAME,
    language: fileNameToLanguage(APP_COMPONENT_FILE_NAME),
    value: App,
  },
  "App.css": {
    name: "App.css",
    language: "css",
    value: AppCss,
  },
  [IMPORT_MAP_FILE_NAME]: {
    name: IMPORT_MAP_FILE_NAME,
    language: fileNameToLanguage(IMPORT_MAP_FILE_NAME),
    value: importMap,
  },
};
