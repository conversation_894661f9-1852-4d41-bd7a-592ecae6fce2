<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/subs/react-playground/vite.svg">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React PlayGround</title>
    <style>
      /* 子应用加载动画 - 只在容器内显示 */
      #root {
        height: 100%;
        min-height: 600px;
      }

      .app-loading {
        margin-top: 100px;
        position: relative;
        width: 100%;
        height: 100%;
        background: #fff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .loading-spinner {
        position: relative;
        width: 80px;
        height: 80px;
        margin-bottom: 24px;
        perspective: 200px;
      }

      .code-box {
        position: relative;
        width: 60px;
        height: 60px;
        margin: 0 auto;
        transform-style: preserve-3d;
        animation: rotate3d 3s linear infinite;
      }

      .box-face {
        position: absolute;
        width: 60px;
        height: 60px;
        border: 2px solid #007acc;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Courier New", monospace;
        font-size: 12px;
        font-weight: bold;
        color: #007acc;
      }

      .box-front {
        transform: translateZ(30px);
        background: linear-gradient(135deg, #007acc 0%, #0056b3 100%);
        color: white;
      }

      .box-back {
        transform: translateZ(-30px) rotateY(180deg);
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        color: white;
      }

      .box-right {
        transform: rotateY(90deg) translateZ(30px);
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        color: #333;
      }

      .box-left {
        transform: rotateY(-90deg) translateZ(30px);
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
      }

      .box-top {
        transform: rotateX(90deg) translateZ(30px);
        background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
        color: white;
      }

      .box-bottom {
        transform: rotateX(-90deg) translateZ(30px);
        background: linear-gradient(135deg, #fd7e14 0%, #e55a00 100%);
        color: white;
      }

      .loading-dots {
        display: flex;
        gap: 8px;
        margin-bottom: 16px;
      }

      .loading-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #007acc;
        animation: bounce 1.4s ease-in-out infinite both;
      }

      .loading-dot:nth-child(1) {
        animation-delay: -0.32s;
      }
      .loading-dot:nth-child(2) {
        animation-delay: -0.16s;
      }
      .loading-dot:nth-child(3) {
        animation-delay: 0s;
      }

      .loading-text {
        color: #000;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        font-size: 16px;
        margin-bottom: 8px;
      }

      .loading-detail {
        color: #999;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        font-size: 12px;
        text-align: center;
        max-width: 300px;
        line-height: 1.4;
      }

      @keyframes rotate3d {
        0% {
          transform: rotateX(0deg) rotateY(0deg);
        }
        25% {
          transform: rotateX(90deg) rotateY(0deg);
        }
        50% {
          transform: rotateX(90deg) rotateY(90deg);
        }
        75% {
          transform: rotateX(0deg) rotateY(90deg);
        }
        100% {
          transform: rotateX(0deg) rotateY(360deg);
        }
      }

      @keyframes bounce {
        0%,
        80%,
        100% {
          transform: scale(0);
          opacity: 0.5;
        }
        40% {
          transform: scale(1);
          opacity: 1;
        }
      }

      /* 当应用加载完成后隐藏 loading */
      .app-loaded .app-loading {
        display: none;
      }
    </style>
    <script crossorigin="">import('/subs/react-playground/js/index-PHLgwyiG.js').finally(() => {
            
    const qiankunLifeCycle = window.moudleQiankunAppLifeCycles && window.moudleQiankunAppLifeCycles['react-playground'];
    if (qiankunLifeCycle) {
      window.proxy.vitemount((props) => qiankunLifeCycle.mount(props));
      window.proxy.viteunmount((props) => qiankunLifeCycle.unmount(props));
      window.proxy.vitebootstrap(() => qiankunLifeCycle.bootstrap());
      window.proxy.viteupdate((props) => qiankunLifeCycle.update(props));
    }
  
          })</script>
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/@<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/react-dom@<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/allotment@1.20.4_react-dom@<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/@<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/@monaco-editor_react@4.7.0-rc.0_monaco-editor@0.52.2_react-dom@<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/@typescript_ata@<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/<EMAIL>">
    <link rel="modulepreload" crossorigin="" href="/subs/react-playground/js/vite-plugin-qiankun@1.0.15_typescript@<EMAIL>">
    <link rel="stylesheet" crossorigin="" href="/subs/react-playground/css/allotment@1.20.4_react-dom@<EMAIL>">
    <link rel="stylesheet" crossorigin="" href="/subs/react-playground/css/<EMAIL>">
    <link rel="stylesheet" crossorigin="" href="/subs/react-playground/css/index-B7td_v4_.css">
  </head>
  <body>
    <div id="root">
      <!-- 子应用加载动画 -->
      <div class="app-loading">
        <div class="loading-spinner">
          <div class="code-box">
            <div class="box-face box-front">React</div>
            <div class="box-face box-back">TS</div>
            <div class="box-face box-right">JS</div>
            <div class="box-face box-left">CSS</div>
            <div class="box-face box-top">HTML</div>
            <div class="box-face box-bottom">JSON</div>
          </div>
        </div>
        <div class="loading-dots">
          <div class="loading-dot"></div>
          <div class="loading-dot"></div>
          <div class="loading-dot"></div>
        </div>
        <div class="loading-text">正在加载 React Playground...</div>
        <div class="loading-detail">
          Monaco Editor、TypeScript 编译器等大型资源加载中，请稍候
        </div>
      </div>
    </div>
  

<script>
  const createDeffer = (hookName) => {
    const d = new Promise((resolve, reject) => {
      window.proxy && (window.proxy[`vite${hookName}`] = resolve)
    })
    return props => d.then(fn => fn(props));
  }
  const bootstrap = createDeffer('bootstrap');
  const mount = createDeffer('mount');
  const unmount = createDeffer('unmount');
  const update = createDeffer('update');

  ;(global => {
    global.qiankunName = 'react-playground';
    global['react-playground'] = {
      bootstrap,
      mount,
      unmount,
      update
    };
  })(window);
</script></body></html>