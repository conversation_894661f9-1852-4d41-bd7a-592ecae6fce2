import { strFromU8, strToU8, unzlibSync, zlibSync } from "fflate";
import J<PERSON><PERSON><PERSON> from "jszip";
import { saveAs } from "file-saver";
import type { Files } from "../types";

export const fileNameToLanguage = (name: string) => {
  const suffix = name.split(".").pop() || "";
  if (["js", "jsx"].includes(suffix)) return "javascript";
  if (["ts", "tsx"].includes(suffix)) return "typescript";
  if (["json"].includes(suffix)) return "json";
  if (["css"].includes(suffix)) return "css";
  return "javascript";
};

/**
 * 压缩字符串
 * @param data 字符串
 * @returns 压缩后的字符串
 */
export function compress(data: string): string {
  try {
    const buffer = strToU8(data);
    const zipped = zlibSync(buffer, { level: 9 });
    const str = strFromU8(zipped, true);
    return btoa(str);
  } catch (error) {
    console.error('压缩失败:', error);
    throw new Error('数据压缩失败');
  }
}

/**
 * 解压缩字符串
 * @param base64 压缩后的字符串
 * @returns 解压缩后的字符串
 */
export function uncompress(base64: string): string {
  try {
    // 验证 base64 格式
    if (!base64 || typeof base64 !== 'string') {
      throw new Error('无效的 base64 字符串');
    }

    const binary = atob(base64);
    const buffer = strToU8(binary, true);
    
    // 检查 buffer 是否为空
    if (buffer.length === 0) {
      throw new Error('解压缩数据为空');
    }

    const unzipped = unzlibSync(buffer);
    return strFromU8(unzipped);
  } catch (error) {
    console.error('解压缩失败:', error);
    throw new Error('数据解压缩失败: ' + (error instanceof Error ? error.message : '未知错误'));
  }
}

export async function downloadFiles(files: Files) {
  const zip = new JSZip();

  Object.keys(files).forEach((name) => {
    zip.file(name, files[name].value);
  });

  const blob = await zip.generateAsync({ type: "blob" });
  saveAs(blob, `code${Math.random().toString().slice(2, 8)}.zip`);
}
