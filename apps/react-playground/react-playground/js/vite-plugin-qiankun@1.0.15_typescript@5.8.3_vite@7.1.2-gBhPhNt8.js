var n={};/*!
 * vite-plugin-qiankun.js v1.0.14
 * (c) 2021-2022 <PERSON><PERSON>
 * Released under the MIT License.
 */var r;function d(){if(r)return n;r=1,Object.defineProperty(n,"__esModule",{value:!0});var e=typeof window<"u"?window.proxy||window:{},i=function(u){e?.__POWERED_BY_QIANKUN__&&(window.moudleQiankunAppLifeCycles||(window.moudleQiankunAppLifeCycles={}),e.qiankunName&&(window.moudleQiankunAppLifeCycles[e.qiankunName]=u))};return n.default=i,n.qiankunWindow=e,n.renderWithQiankun=i,n}var o=d();export{o as h};
